import { motion } from 'framer-motion'

const Values = () => {
  const values = [
    {
      title: 'Quality Excellence',
      description: 'We maintain the highest standards in all our products and services.',
      icon: '⭐'
    },
    {
      title: 'Integrity',
      description: 'We conduct business with honesty, transparency, and ethical practices.',
      icon: '🤝'
    },
    {
      title: 'Customer Focus',
      description: 'Our customers are at the center of everything we do.',
      icon: '🎯'
    },
    {
      title: 'Innovation',
      description: 'We continuously seek new ways to improve and innovate.',
      icon: '💡'
    },
    {
      title: 'Reliability',
      description: 'We deliver on our promises consistently and dependably.',
      icon: '🔒'
    },
    {
      title: 'Sustainability',
      description: 'We are committed to sustainable and responsible business practices.',
      icon: '🌱'
    }
  ]

  return (
    <section className="section">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-primary mb-6">Our Values</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            These core values guide our decisions and shape our company culture.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {values.map((value, index) => (
            <motion.div
              key={value.title}
              className="text-center p-6 bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-4xl mb-4">{value.icon}</div>
              <h3 className="text-xl font-semibold text-primary mb-3">{value.title}</h3>
              <p className="text-gray-600">{value.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Values
