import { motion } from 'framer-motion'

const AboutSection = () => {
  return (
    <section className="section bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-primary mb-6">About AMM</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Anuradha Minerals and Metals (AMM) is a newly established trading company 
            led by Managing Director <PERSON><PERSON><PERSON>, specializing in high-quality minerals and metals.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[
            {
              title: 'Our Mission',
              description: 'To provide high-quality minerals and metals while maintaining the highest standards of customer service and business ethics.',
              icon: '🎯'
            },
            {
              title: 'Our Vision',
              description: 'To be recognized as a trusted global partner in the minerals and metals trading industry.',
              icon: '🌟'
            },
            {
              title: 'Our Values',
              description: 'Excellence, integrity, and customer satisfaction are at the core of everything we do.',
              icon: '💎'
            }
          ].map((item, index) => (
            <motion.div
              key={item.title}
              className="bg-white p-8 rounded-lg shadow-lg text-center"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-4xl mb-4">{item.icon}</div>
              <h3 className="text-xl font-semibold text-primary mb-4">{item.title}</h3>
              <p className="text-gray-600">{item.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default AboutSection
