import { motion } from 'framer-motion'

const AboutSection = () => {
  const industrialImages = [
    {
      title: 'Our Mission',
      description: 'To provide high-quality minerals and metals while maintaining the highest standards of customer service and business ethics.',
      image: 'https://images.unsplash.com/photo-1565793298595-6a879b1d9492?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Steel Plant Operations'
    },
    {
      title: 'Our Vision',
      description: 'To be recognized as a trusted global partner in the minerals and metals trading industry.',
      image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Industrial Manufacturing'
    },
    {
      title: 'Our Values',
      description: 'Excellence, integrity, and customer satisfaction are at the core of everything we do.',
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      alt: 'Quality Control'
    }
  ]

  return (
    <section className="section bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-primary mb-6">About AMM</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Anuradha Minerals and Metals (AMM) is a newly established trading company
            led by Managing Director Anuradha Singh, specializing in high-quality minerals and metals.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {industrialImages.map((item, index) => (
            <motion.div
              key={item.title}
              className="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-2xl transition-all duration-500"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={item.image}
                  alt={item.alt}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/80 to-transparent" />
                <div className="absolute bottom-4 left-4 right-4">
                  <h3 className="text-xl font-semibold text-white mb-2">{item.title}</h3>
                </div>
              </div>
              <div className="p-6">
                <p className="text-gray-600 leading-relaxed">{item.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default AboutSection
