@echo off
echo ========================================
echo   AMM Website Deployment to GitHub Pages
echo ========================================
echo.

echo Step 1: Installing gh-pages...
npm install --save-dev gh-pages

echo.
echo Step 2: Building the website...
npm run build

echo.
echo Step 3: Deploying to GitHub Pages...
npx gh-pages -d dist

echo.
echo ========================================
echo   Deployment Complete!
echo ========================================
echo Your website will be live at:
echo https://hiabhik.github.io/AMM/
echo.
echo You can then configure a custom domain (ammindia.in) in GitHub settings
echo.
pause
