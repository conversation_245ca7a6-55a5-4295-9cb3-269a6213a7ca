import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'

const CtaSection = () => {
  return (
    <section className="section bg-secondary text-white">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-6">
            Ready to Partner with AMM?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join hundreds of satisfied customers who trust Anuradha Minerals and Metals 
            for their mineral and metal requirements. Experience excellence in quality and service.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="bg-white text-secondary hover:bg-gray-100 px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              Get Quote
            </Link>
            <Link
              to="/products"
              className="bg-primary hover:bg-primary-light text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              View Products
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default CtaSection
