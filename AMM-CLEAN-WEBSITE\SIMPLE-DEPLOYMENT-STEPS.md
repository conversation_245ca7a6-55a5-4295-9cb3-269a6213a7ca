# 🚀 Simple Steps to Deploy AMM Website to ammindia.in

## Method 1: Netlify (Easiest - Recommended)

### Step 1: Go to Netlify
1. Open browser and go to: **https://app.netlify.com**
2. Click **"Sign up"** 
3. Choose **"Sign up with GitHub"**
4. Allow Netlify to access your GitHub

### Step 2: Deploy Your Site
1. Click **"Add new site"** button
2. Choose **"Import an existing project"**
3. Click **"Deploy with GitHub"**
4. Find and click on **"AMM"** repository
5. Configure settings:
   - **Base directory:** `AMM-CLEAN-WEBSITE`
   - **Build command:** `npm run build`
   - **Publish directory:** `AMM-CLEAN-WEBSITE/dist`
6. Click **"Deploy site"**

### Step 3: Wait for Deployment
- <PERSON><PERSON> will automatically build and deploy your site
- This takes 2-3 minutes
- You'll get a temporary URL like: `https://amazing-name-123456.netlify.app`

### Step 4: Add Custom Domain
1. Go to **Site settings** → **Domain management**
2. Click **"Add custom domain"**
3. Enter: **ammindia.in**
4. Click **"Verify"**
5. Follow DNS instructions (see below)

### Step 5: Configure DNS (Domain Settings)
1. Go to your domain provider (where you bought ammindia.in)
2. Find **DNS settings** or **DNS management**
3. Add these records:
   - **Type:** A Record, **Name:** @, **Value:** 75.2.60.5
   - **Type:** CNAME, **Name:** www, **Value:** your-site.netlify.app

---

## Method 2: Vercel (Alternative)

### Step 1: Go to Vercel
1. Open: **https://vercel.com**
2. Click **"Sign up with GitHub"**

### Step 2: Import Project
1. Click **"Add New Project"**
2. Find **"AMM"** repository
3. Click **"Import"**
4. Set **Root Directory:** `AMM-CLEAN-WEBSITE`
5. Click **"Deploy"**

### Step 3: Add Domain
1. Go to project settings
2. Add domain: **ammindia.in**
3. Configure DNS as instructed

---

## Method 3: GitHub Pages (Free)

### Step 1: Enable GitHub Pages
1. Go to: **https://github.com/hiabhik/AMM**
2. Click **"Settings"** tab
3. Scroll to **"Pages"** section
4. Source: **"Deploy from a branch"**
5. Branch: **"gh-pages"** (will be created automatically)
6. Click **"Save"**

### Step 2: Run Deployment Script
1. Double-click **"deploy-github-pages.bat"** file
2. Wait for deployment to complete
3. Your site will be at: **https://hiabhik.github.io/AMM/**

### Step 3: Add Custom Domain
1. In GitHub Pages settings
2. Add custom domain: **ammindia.in**
3. Configure DNS with your domain provider

---

## 🎯 Recommended: Use Netlify Method 1

**Why Netlify is best:**
- ✅ Easiest setup
- ✅ Automatic HTTPS
- ✅ Fast global CDN
- ✅ Free plan available
- ✅ Perfect for React websites

---

## 📞 Need Help?

If you get stuck:
1. **Netlify Support:** https://docs.netlify.com
2. **Domain DNS Help:** Contact your domain provider
3. **GitHub Pages:** https://pages.github.com

---

## 🌟 After Deployment

Your website will be live at:
- **https://ammindia.in**
- **https://www.ammindia.in**

**Features that will work:**
- ✅ All 8 products displayed
- ✅ Contact form functional
- ✅ Mobile responsive
- ✅ Fast loading
- ✅ Professional design
- ✅ Global accessibility

---

## 🔧 Files Ready for Deployment

All these files are already prepared:
- ✅ Production build (`dist` folder)
- ✅ Netlify configuration (`netlify.toml`)
- ✅ Routing setup (`_redirects`)
- ✅ Deployment scripts (`.bat` files)
- ✅ GitHub repository updated

**Just follow Method 1 (Netlify) - it's the easiest!**
