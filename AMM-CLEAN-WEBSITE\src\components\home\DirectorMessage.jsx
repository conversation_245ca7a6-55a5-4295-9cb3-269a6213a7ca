import { motion } from 'framer-motion'

const DirectorMessage = () => {
  return (
    <section className="section bg-primary text-white">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-4">
            Message from the <span className="text-secondary">Managing Director</span>
          </h2>
          <div className="w-24 h-1 bg-secondary mx-auto rounded-full"></div>
        </motion.div>

        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* Director Image */}
          <motion.div
            className="lg:w-1/3"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="relative rounded-lg overflow-hidden shadow-2xl">
              <img
                src="/images/anuimage.jpeg"
                alt="<PERSON><PERSON><PERSON> <PERSON> - Managing Director"
                className="w-full rounded-lg"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = 'https://placehold.co/400x500/0F3460/C9A664?text=Anuradha+Singh';
                }}
              />
              <div className="absolute inset-0 border-2 border-secondary rounded-lg"></div>
            </div>
            <div className="text-center mt-4">
              <h3 className="text-xl font-bold text-secondary">Anuradha Singh</h3>
              <p className="text-gray-300">Managing Director</p>
            </div>
          </motion.div>

          {/* Message Content */}
          <motion.div
            className="lg:w-2/3"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <div className="bg-primary-light/50 backdrop-blur-sm p-8 rounded-lg border border-secondary/20">
              <p className="text-gray-300 mb-6 leading-relaxed text-lg">
                "At Anuradha Minerals & Metals, we are committed to excellence in every aspect of our business.
                Our journey began with a vision to become a leading provider of high-quality minerals and metals,
                and today, I am proud to say that we have established ourselves as a trusted name in the industry."
              </p>
              <p className="text-gray-300 mb-6 leading-relaxed text-lg">
                "Our success is built on the foundation of quality, integrity, and customer satisfaction.
                We continuously strive to innovate and improve our processes to deliver products that meet
                the highest standards of quality and reliability."
              </p>
              <p className="text-gray-300 leading-relaxed text-lg mb-8">
                "As we look to the future, we remain dedicated to sustainable growth and development,
                creating value for our customers, employees, and stakeholders. Thank you for your trust and support."
              </p>

              <div className="flex items-center">
                <div className="w-16 h-[2px] bg-secondary mr-4"></div>
                <p className="text-secondary font-semibold text-lg">Anuradha Singh</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default DirectorMessage
