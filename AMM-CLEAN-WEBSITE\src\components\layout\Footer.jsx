import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const footerSections = [
    {
      title: 'Steel Industry',
      links: [
        { name: 'Blast Furnace Materials', path: '/products' },
        { name: 'EAF Grade Scrap', path: '/products' },
        { name: 'Steel Mill Services', path: '/about' },
        { name: 'Quality Certifications', path: '/about' },
      ]
    },
    {
      title: 'Our Products',
      links: [
        { name: 'Aluminium Ingots', path: '/products' },
        { name: 'Calcined Lime', path: '/products' },
        { name: 'Industrial Glycerin', path: '/products' },
        { name: 'Steel Scrap', path: '/products' },
      ]
    },
    {
      title: 'Industries Served',
      content: [
        '🏭 Blast Furnace Operations',
        '⚡ Electric Arc Furnaces',
        '🔄 Steel Rolling Mills',
        '🔥 Steel Foundries',
      ]
    },
    {
      title: 'Contact AMM',
      content: [
        '👤 MD: An<PERSON><PERSON>',
        '🌐 www.ammindia.in',
        '📧 Steel Industry Focus',
        '🏢 Global Steel Supply',
      ]
    }
  ]

  return (
    <footer className="bg-primary text-white relative overflow-hidden">
      {/* Steel Industry Background */}
      <div className="absolute inset-0 opacity-5">
        <img
          src="https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          alt="Steel Industry Background"
          className="w-full h-full object-cover"
        />
      </div>

      <div className="container mx-auto px-4 py-16 relative z-10">
        {/* Company Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="flex items-center justify-center mb-4">
            <img
              src="/images/amm-logo.svg"
              alt="AMM Logo"
              className="h-16 w-auto mr-4"
              style={{ filter: 'drop-shadow(0 0 10px rgba(201, 166, 100, 0.5))' }}
            />
            <div>
              <h2 className="text-3xl font-bold text-secondary">AMM</h2>
              <p className="text-gray-300">Anuradha Minerals and Metals</p>
            </div>
          </div>
          <p className="text-gray-300 max-w-2xl mx-auto">
            Leading supplier of premium materials for blast furnaces, electric arc furnaces,
            and steel manufacturing facilities worldwide.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">

          {/* Footer Sections with 3D Effects */}
          {footerSections.map((section, index) => (
            <div key={section.title} className="md:col-span-1">
              <motion.div
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-secondary/20 hover:border-secondary/40 transition-all duration-500"
                initial={{ opacity: 0, y: 30, rotateX: -15 }}
                whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{
                  scale: 1.02,
                  rotateY: 5,
                  boxShadow: "0 20px 40px rgba(201, 166, 100, 0.2)"
                }}
                style={{
                  transformStyle: "preserve-3d",
                  perspective: "1000px"
                }}
              >
                <h4 className="text-lg font-semibold mb-4 text-secondary">{section.title}</h4>
                {section.links ? (
                  <ul className="space-y-3">
                    {section.links.map((link) => (
                      <li key={link.name}>
                        <Link
                          to={link.path}
                          className="text-gray-300 hover:text-white transition-colors duration-300 flex items-center group"
                        >
                          <span className="mr-2 group-hover:translate-x-1 transition-transform duration-300">→</span>
                          {link.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="space-y-3">
                    {section.content.map((item, idx) => (
                      <p key={idx} className="text-gray-300 text-sm flex items-center">
                        {item}
                      </p>
                    ))}
                  </div>
                )}
              </motion.div>
            </div>
          ))}
        </div>

        {/* Enhanced Bottom Bar */}
        <motion.div
          className="border-t border-secondary/30 mt-12 pt-8"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <p className="text-gray-300 text-sm">
                © {currentYear} Anuradha Minerals and Metals. All rights reserved.
              </p>
              <p className="text-secondary text-xs mt-1">
                Trusted Steel Industry Partner | www.ammindia.in
              </p>
            </div>
            <div className="flex space-x-6 text-sm text-gray-400">
              <span className="flex items-center">
                <span className="mr-2">🏭</span>
                Steel Mills
              </span>
              <span className="flex items-center">
                <span className="mr-2">⚡</span>
                EAF Operations
              </span>
              <span className="flex items-center">
                <span className="mr-2">🔥</span>
                Blast Furnaces
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
