import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const footerSections = [
    {
      title: 'Company',
      links: [
        { name: 'About Us', path: '/about' },
        { name: 'Our Products', path: '/products' },
        { name: 'Contact', path: '/contact' },
      ]
    },
    {
      title: 'Products',
      links: [
        { name: 'Aluminium Ingots', path: '/products' },
        { name: 'Calcined Lime', path: '/products' },
        { name: 'Glycerin', path: '/products' },
        { name: 'Scrap Materials', path: '/products' },
      ]
    },
    {
      title: 'Contact Info',
      content: [
        'Managing Director: <PERSON><PERSON><PERSON>',
        'Website: www.ammindia.in',
        'Company: Anuradha Minerals and Metals',
      ]
    }
  ]

  return (
    <footer className="bg-primary text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="md:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold mb-4">AMM</h3>
              <p className="text-gray-300 mb-4">
                Anuradha Minerals and Metals - Leading the future of minerals and metals trading.
              </p>
              <p className="text-secondary text-sm font-semibold">
                Excellence in Quality & Service
              </p>
            </motion.div>
          </div>

          {/* Footer Sections */}
          {footerSections.map((section, index) => (
            <div key={section.title} className="md:col-span-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <h4 className="text-lg font-semibold mb-4">{section.title}</h4>
                {section.links ? (
                  <ul className="space-y-2">
                    {section.links.map((link) => (
                      <li key={link.name}>
                        <Link
                          to={link.path}
                          className="text-gray-300 hover:text-secondary transition-colors duration-300"
                        >
                          {link.name}
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="space-y-2">
                    {section.content.map((item, idx) => (
                      <p key={idx} className="text-gray-300 text-sm">
                        {item}
                      </p>
                    ))}
                  </div>
                )}
              </motion.div>
            </div>
          ))}
        </div>

        {/* Bottom Bar */}
        <motion.div
          className="border-t border-gray-600 mt-8 pt-8 text-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <p className="text-gray-300 text-sm">
            © {currentYear} Anuradha Minerals and Metals. All rights reserved.
          </p>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
