import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'

const HeroSection = () => {
  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Blast Furnace Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-black/80 z-10" />
        <img
          src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          alt="Blast Furnace Steel Manufacturing"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-primary/40 to-transparent" />
      </div>

      {/* Floating Steel Manufacturing Elements with 3D Effect */}
      <div className="absolute inset-0 z-5">
        {[
          { img: "https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80", pos: "top-20 left-20", delay: 0, title: "Blast Furnace" },
          { img: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80", pos: "top-40 right-20", delay: 0.5, title: "Steel Production" },
          { img: "https://images.unsplash.com/photo-1587293852726-70cdb56c2866?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80", pos: "bottom-40 left-32", delay: 1, title: "Metal Ingots" },
          { img: "https://images.unsplash.com/photo-1565793298595-6a879b1d9492?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80", pos: "bottom-20 right-32", delay: 1.5, title: "Steel Mill" }
        ].map((item, index) => (
          <motion.div
            key={index}
            className={`absolute ${item.pos} w-28 h-28 rounded-lg overflow-hidden opacity-40 border-2 border-secondary/60 shadow-2xl`}
            initial={{ opacity: 0, scale: 0.5, rotate: -10, z: -50 }}
            animate={{ opacity: 0.4, scale: 1, rotate: 0, z: 0 }}
            transition={{ duration: 1, delay: item.delay }}
            whileHover={{
              scale: 1.2,
              opacity: 0.8,
              rotateY: 15,
              z: 50,
              boxShadow: "0 25px 50px rgba(201, 166, 100, 0.3)"
            }}
            style={{
              transformStyle: "preserve-3d",
              perspective: "1000px"
            }}
          >
            <img src={item.img} alt={item.title} className="w-full h-full object-cover" />
            <div className="absolute inset-0 bg-gradient-to-t from-primary/60 to-transparent" />
            <div className="absolute bottom-1 left-1 right-1 text-white text-xs font-bold text-center">
              {item.title}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center text-white">
          <motion.h1
            className="text-5xl md:text-7xl font-bold mb-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            style={{
              background: 'linear-gradient(to right, #ffffff, #c9a664)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            Anuradha Minerals & Metals
          </motion.h1>
          
          <motion.p
            className="text-xl md:text-2xl mb-8 text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Leading the Future of Minerals and Metals Trading
          </motion.p>
          
          <motion.p
            className="text-lg mb-12 text-secondary font-semibold"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            Excellence in Quality & Service | www.ammindia.in
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <Link
              to="/products"
              className="bg-primary hover:bg-primary-light text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              Explore Products
            </Link>
            <Link
              to="/contact"
              className="bg-secondary hover:bg-secondary-light text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              Contact Us
            </Link>
          </motion.div>
        </div>
      </div>

      {/* Enhanced 3D Decorative Elements */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
        <motion.div
          className="flex space-x-8 text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          {[
            { name: 'Aluminium Ingots', icon: '🔩' },
            { name: 'Calcined Lime', icon: '⚪' },
            { name: 'Industrial Glycerin', icon: '💧' },
            { name: 'Steel Scrap', icon: '♻️' }
          ].map((product, index) => (
            <motion.div
              key={product.name}
              className="text-white group cursor-pointer"
              whileHover={{
                scale: 1.2,
                rotateY: 15,
                z: 30
              }}
              style={{
                transformStyle: "preserve-3d",
                perspective: "1000px"
              }}
            >
              <motion.div
                className="w-12 h-12 bg-secondary/20 backdrop-blur-sm rounded-full mx-auto mb-2 flex items-center justify-center border border-secondary/40 group-hover:border-secondary group-hover:bg-secondary/40 transition-all duration-300"
                animate={{
                  rotateY: [0, 360],
                }}
                transition={{
                  duration: 8 + index * 2,
                  repeat: Infinity,
                  ease: "linear"
                }}
              >
                <span className="text-xl">{product.icon}</span>
              </motion.div>
              <p className="text-sm font-medium group-hover:text-secondary transition-colors duration-300">{product.name}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Floating 3D AMM Logo */}
      <motion.div
        className="absolute top-1/4 right-10 hidden lg:block"
        initial={{ opacity: 0, scale: 0.5, rotateY: -90 }}
        animate={{ opacity: 0.6, scale: 1, rotateY: 0 }}
        transition={{ duration: 1.5, delay: 1 }}
        whileHover={{
          scale: 1.2,
          rotateY: 180,
          opacity: 1
        }}
        style={{
          transformStyle: "preserve-3d",
          perspective: "1000px"
        }}
      >
        <div className="w-24 h-24 bg-gradient-to-br from-secondary to-primary rounded-lg flex items-center justify-center text-white font-bold text-xl shadow-2xl border border-secondary/50">
          AMM
        </div>
      </motion.div>
    </section>
  )
}

export default HeroSection
