# Anuradha Minerals and Metals Website

This is the official website for Anuradha Minerals and Metals, a dynamic trading company specializing in high-quality minerals and metals.

## Project Structure

```
AMM-PROJECT/
├── dist/             # Production-ready files (generated)
├── src/              # Source files
│   ├── images/       # Image files
│   ├── *.html        # HTML files
│   ├── *.css         # CSS files
│   └── *.js          # JavaScript files
├── package.json      # Project configuration
└── README.md         # Project documentation
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository
   ```
   git clone https://github.com/hiabhik/AMM.git
   cd AMM-PROJECT
   ```

2. Install dependencies
   ```
   npm install
   ```

### Development

To start the development server:

```
npm start
```

This will start a local server at http://localhost:8080

### Building for Production

To build the project for production:

```
npm run build
```

This will create a production-ready version in the `dist` directory.

### Deployment

To deploy to Netlify:

```
npm run deploy
```

## Features

- Responsive design for all devices
- Modern UI with animations
- Product showcase
- Contact form
- Company information

## Technologies Used

- HTML5
- CSS3
- JavaScript
- Font Awesome
- Google Fonts

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Contact

For any inquiries, please contact:
- Email: <EMAIL>
- Phone: +91 ************
