Option Explicit

Dim objShell, objFS<PERSON>, strChromePath, strCurrentDir, strHTMLPath

' Create Shell and FileSystem objects
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Try to find Chrome in common installation locations
strChromePath = "C:\Program Files\Google\Chrome\Application\chrome.exe"
If Not objFSO.FileExists(strChromePath) Then
    strChromePath = "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
End If
If Not objFSO.FileExists(strChromePath) Then
    strChromePath = objShell.ExpandEnvironmentStrings("%LOCALAPPDATA%") & "\Google\Chrome\Application\chrome.exe"
End If

' Get the current directory and HTML path
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)
strHTMLPath = strCurrentDir & "\src\index.html"

' Open Chrome with the website
If objFSO.FileExists(strChromePath) Then
    WScript.Echo "Opening AMM Website in Chrome..."
    objShell.Run """" & strChromePath & """ """ & strHTMLPath & """", 1, False
Else
    WScript.Echo "Chrome not found in common locations. Opening in default browser instead..."
    objShell.Run """" & strHTMLPath & """", 1, False
End If

Set objShell = Nothing
Set objFSO = Nothing
