<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Anuradha Minerals and Metals</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Rajdhani:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Additional styles for Contact page */
        .contact-hero {
            min-height: 60vh;
        }

        .contact-content {
            display: flex;
            flex-direction: column;
            gap: 3rem;
            margin-bottom: 4rem;
        }

        @media (min-width: 768px) {
            .contact-content {
                flex-direction: row;
            }
        }

        .contact-info {
            flex: 1;
            background: rgba(245, 240, 225, 0.95);
            border: 1px solid rgba(201, 166, 100, 0.3);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 0 20px rgba(201, 166, 100, 0.1);
        }

        .contact-info h3 {
            font-size: 1.8rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 1px;
            position: relative;
            padding-bottom: 0.8rem;
        }

        .contact-info h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 2px;
            background: linear-gradient(to right, var(--secondary), transparent);
        }

        .contact-details {
            margin-bottom: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: rgba(245, 240, 225, 0.95);
            border: 1px solid rgba(201, 166, 100, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: var(--primary);
            flex-shrink: 0;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), 0 0 15px rgba(201, 166, 100, 0.1);
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .contact-item:hover .contact-icon {
            transform: scale(1.1) translateY(-5px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15), 0 0 20px rgba(201, 166, 100, 0.2);
            color: var(--secondary);
        }

        .contact-text h4 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--secondary);
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
        }

        .contact-text p {
            color: #333333;
            font-size: 1rem;
            line-height: 1.5;
            font-weight: 500;
        }

        .contact-form {
            flex: 1;
            background: rgba(245, 240, 225, 0.95);
            border: 1px solid rgba(201, 166, 100, 0.3);
            border-radius: 8px;
            padding: 2.5rem;
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 0 20px rgba(201, 166, 100, 0.1);
            position: relative;
            overflow: hidden;
        }

        .contact-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at top right, rgba(201, 166, 100, 0.05), transparent 70%);
            z-index: 0;
        }

        .contact-form:hover {
            transform: translateY(-10px) translateZ(20px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 30px rgba(201, 166, 100, 0.2);
            border-color: rgba(201, 166, 100, 0.5);
        }

        .contact-form h3 {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 2rem;
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 1px;
            position: relative;
            padding-bottom: 0.8rem;
            z-index: 1;
        }

        .contact-form h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 2px;
            background: linear-gradient(to right, var(--secondary), transparent);
        }

        .form-group {
            margin-bottom: 1.8rem;
            position: relative;
            z-index: 1;
        }

        .form-label {
            display: block;
            margin-bottom: 0.8rem;
            font-weight: 500;
            color: var(--primary);
            font-size: 1rem;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            transform-origin: left;
        }

        .form-group:focus-within .form-label {
            transform: scale(1.05);
            color: var(--secondary);
        }

        .form-control {
            width: 100%;
            padding: 1rem 1.2rem;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(201, 166, 100, 0.3);
            border-radius: 6px;
            color: var(--text-dark);
            transition: all 0.3s ease;
            font-size: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .form-control:focus {
            outline: none;
            border-color: rgba(201, 166, 100, 0.7);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), 0 0 15px rgba(201, 166, 100, 0.2);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-3px);
        }

        textarea.form-control {
            min-height: 150px;
            resize: vertical;
        }

        .form-submit {
            width: 100%;
            padding: 1.2rem;
            background: linear-gradient(145deg, rgba(10, 15, 44, 0.9), rgba(6, 10, 30, 0.95));
            border: 1px solid rgba(201, 166, 100, 0.3);
            border-radius: 6px;
            color: var(--cream);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            font-size: 1.1rem;
            letter-spacing: 1px;
            text-transform: uppercase;
            font-family: 'Rajdhani', sans-serif;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 0 15px rgba(201, 166, 100, 0.1);
            z-index: 1;
        }

        .form-submit:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 0 20px rgba(201, 166, 100, 0.3);
            border-color: rgba(201, 166, 100, 0.5);
        }

        .form-submit::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(201, 166, 100, 0.2), transparent);
            transition: all 0.6s ease;
            z-index: -1;
        }

        .form-submit:hover::before {
            left: 100%;
            transition: 0.8s;
        }

        /* Add floating elements to form */
        .contact-form::after {
            content: '';
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 1px dashed rgba(201, 166, 100, 0.3);
            bottom: 30px;
            right: 30px;
            animation: rotate 20s linear infinite;
            opacity: 0.5;
            z-index: 0;
        }

        .map-container {
            height: 400px;
            background: rgba(245, 240, 225, 0.95);
            border: 1px solid rgba(201, 166, 100, 0.3);
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 4rem;
            position: relative;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 0 20px rgba(201, 166, 100, 0.1);
            transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .map-container:hover {
            transform: translateY(-10px) translateZ(20px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 30px rgba(201, 166, 100, 0.2);
            border-color: rgba(201, 166, 100, 0.5);
        }

        .map-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1.5rem;
            background: linear-gradient(135deg, rgba(245, 240, 225, 0.95), rgba(235, 230, 215, 0.95));
        }

        .map-placeholder::before {
            content: '';
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            border: 1px dashed rgba(201, 166, 100, 0.3);
            animation: rotate 30s linear infinite;
            opacity: 0.5;
        }

        .map-placeholder i {
            font-size: 4rem;
            color: var(--primary);
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .map-placeholder:hover i {
            transform: scale(1.1) translateY(-5px);
            color: var(--secondary);
        }

        .map-placeholder p {
            font-size: 1.1rem;
            color: var(--primary);
            position: relative;
            z-index: 1;
            font-family: 'Rajdhani', sans-serif;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <div class="futuristic-background">
        <div class="grid-overlay"></div>
        <div class="glow-effect glow-1"></div>
        <div class="glow-effect glow-2"></div>
        <div class="glow-effect glow-3"></div>
        <div class="light-beam"></div>
        <div class="particles"></div>
    </div>

    <header>
        <div class="container">
            <nav>
                <a href="index.html" class="logo">
                    <img src="images/amm-logo-official.svg" alt="AMM Logo" class="logo-image">
                    <div class="logo-glow"></div>
                </a>

                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="contact.html" class="active">Contact</a></li>
                </ul>

                <div class="menu-toggle">
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                </div>
            </nav>
        </div>
    </header>

    <main>
        <section class="hero contact-hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="glitch-text" data-text="Contact Us">Contact Us</h1>
                    <p class="subtitle">Get in Touch with Our Team</p>
                    <p class="description">Have questions or inquiries? Contact us today and our team will be happy to assist you.</p>
                </div>
            </div>
        </section>

        <section class="contact-section">
            <div class="container">
                <div class="section-header">
                    <h2>Reach Out to Us</h2>
                    <p>We're here to answer any questions you may have</p>
                </div>

                <div class="contact-content">
                    <div class="contact-info">
                        <h3>Contact Information</h3>

                        <div class="contact-details">
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="contact-text">
                                    <h4>Our Location</h4>
                                    <p>123 Business Park, Sector 5</p>
                                    <p>New Delhi, 110001, India</p>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="contact-text">
                                    <h4>Phone Number</h4>
                                    <p>+91 ************</p>
                                    <p>+91 ************</p>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="contact-text">
                                    <h4>Email Address</h4>
                                    <p><EMAIL></p>
                                    <p><EMAIL></p>
                                </div>
                            </div>

                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="contact-text">
                                    <h4>Business Hours</h4>
                                    <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                                    <p>Saturday: 9:00 AM - 1:00 PM</p>
                                </div>
                            </div>
                        </div>

                        <div class="social-links">
                            <a href="#"><i class="fab fa-linkedin"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-facebook"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>

                    <div class="contact-form">
                        <h3>Send Us a Message</h3>

                        <form id="contactForm">
                            <div class="form-group">
                                <label for="name" class="form-label">Your Name</label>
                                <input type="text" id="name" class="form-control" placeholder="Enter your name" required>
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" id="email" class="form-control" placeholder="Enter your email" required>
                            </div>

                            <div class="form-group">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" id="phone" class="form-control" placeholder="Enter your phone number">
                            </div>

                            <div class="form-group">
                                <label for="subject" class="form-label">Subject</label>
                                <input type="text" id="subject" class="form-control" placeholder="Enter subject">
                            </div>

                            <div class="form-group">
                                <label for="message" class="form-label">Message</label>
                                <textarea id="message" class="form-control" placeholder="Enter your message" required></textarea>
                            </div>

                            <button type="submit" class="form-submit">Send Message</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <section class="map-section">
            <div class="container">
                <div class="section-header">
                    <h2>Our Location</h2>
                    <p>Visit us at our office</p>
                </div>

                <div class="map-container">
                    <div class="map-placeholder">
                        <i class="fas fa-map-marked-alt"></i>
                        <p>Map Location: 123 Business Park, Sector 5, New Delhi, India</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="cta">
            <div class="container">
                <div class="cta-content">
                    <h2>Ready to Partner with Us?</h2>
                    <p>Explore our range of high-quality minerals and metals products designed to meet your industry needs.</p>
                    <a href="products.html" class="btn btn-primary">
                        <span>Explore Products</span>
                        <div class="btn-glow"></div>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>Anuradha Minerals & Metals</h3>
                    <p>A dynamic trading company specializing in high-quality minerals and metals. Focused on professionalism, trust, and building long-term partnerships worldwide.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-column">
                    <h3>Products</h3>
                    <ul>
                        <li><a href="products.html">Glycine</a></li>
                        <li><a href="products.html">Scrap (Ferrous & Non-Ferrous)</a></li>
                        <li><a href="products.html">Aluminium Ingots</a></li>
                        <li><a href="products.html">Calcined Lime</a></li>
                        <li><a href="products.html">All Products</a></li>
                    </ul>
                </div>

                <div class="footer-column">
                    <h3>Contact Us</h3>
                    <ul class="contact-info">
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-phone"></i> +91 ************</li>
                        <li><i class="fas fa-map-marker-alt"></i> New Delhi, India</li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2023 Anuradha Minerals and Metals. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
    <script>
        // Form submission handling
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Simulate form submission
            const submitButton = this.querySelector('.form-submit');
            const originalText = submitButton.textContent;

            submitButton.textContent = 'Sending...';
            submitButton.disabled = true;

            // Simulate API call
            setTimeout(() => {
                alert('Thank you for your message! We will get back to you soon.');
                this.reset();
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }, 1500);
        });
    </script>
</body>
</html>
