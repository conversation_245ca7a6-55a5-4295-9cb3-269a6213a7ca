const fs = require('fs');
const path = require('path');

// Create dist directory if it doesn't exist
if (!fs.existsSync('./dist')) {
  fs.mkdirSync('./dist');
}

// Copy all files from src to dist
copyFolderSync('./src', './dist');

console.log('Build completed successfully!');

// Helper function to copy folders recursively
function copyFolderSync(source, target) {
  // Create target folder if it doesn't exist
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target);
  }

  // Copy each file in the source folder
  const files = fs.readdirSync(source);
  files.forEach(file => {
    const sourcePath = path.join(source, file);
    const targetPath = path.join(target, file);

    // If it's a directory, recursively copy it
    if (fs.lstatSync(sourcePath).isDirectory()) {
      copyFolderSync(sourcePath, targetPath);
    } else {
      // Otherwise, copy the file
      fs.copyFileSync(sourcePath, targetPath);
    }
  });
}
