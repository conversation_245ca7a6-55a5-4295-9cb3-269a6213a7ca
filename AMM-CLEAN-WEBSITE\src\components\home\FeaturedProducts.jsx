import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'

const FeaturedProducts = () => {
  const products = [
    {
      name: 'Aluminium Ingots',
      description: 'High-purity aluminium ingots suitable for various industrial applications including automotive, aerospace, and construction.',
      features: ['99.7% purity', 'Various sizes available', 'Certified quality'],
      icon: '🔩'
    },
    {
      name: 'Calcined Lime',
      description: 'Premium quality calcined lime for steel production, water treatment, and chemical processing applications.',
      features: ['High calcium content', 'Low impurities', 'Consistent quality'],
      icon: '⚪'
    },
    {
      name: 'Glycerin',
      description: 'Pure glycerin for pharmaceutical, cosmetic, and food industry applications.',
      features: ['USP grade available', 'Food grade quality', 'Pharmaceutical grade'],
      icon: '💧'
    },
    {
      name: 'Scrap Materials',
      description: 'High-quality ferrous and non-ferrous scrap materials for recycling and reprocessing.',
      features: ['Sorted and graded', 'Competitive pricing', 'Reliable supply'],
      icon: '♻️'
    }
  ]

  return (
    <section className="section">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-primary mb-6">Our Products</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We offer a comprehensive range of high-quality minerals and metals to meet diverse industrial needs.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {products.map((product, index) => (
            <motion.div
              key={product.name}
              className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-4xl mb-4 text-center">{product.icon}</div>
              <h3 className="text-xl font-semibold text-primary mb-3">{product.name}</h3>
              <p className="text-gray-600 mb-4 text-sm">{product.description}</p>
              
              <div className="mb-6">
                <h4 className="font-semibold text-secondary mb-2">Key Features:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  {product.features.map((feature, idx) => (
                    <li key={idx}>• {feature}</li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Link
            to="/products"
            className="bg-primary hover:bg-primary-light text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
          >
            View All Products
          </Link>
        </motion.div>
      </div>
    </section>
  )
}

export default FeaturedProducts
