@echo off
color 0A
echo ======================================================
echo    ANURADHA MINERALS AND METALS - CHROME LAUNCHER
echo ======================================================
echo.
echo Opening AMM Website in Chrome...
echo.

REM Try to find Chrome in common installation locations
set CHROME_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
if not exist %CHROME_PATH% set CHROME_PATH="C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
if not exist %CHROME_PATH% set CHROME_PATH="%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"

REM Get the current directory
set CURRENT_DIR=%~dp0
set HTML_PATH=%CURRENT_DIR%src\index.html

REM Open Chrome with the website
if exist %CHROME_PATH% (
    echo Found Chrome at %CHROME_PATH%
    echo Launching website in Chrome...
    start "" %CHROME_PATH% "%HTML_PATH%"
    echo.
    echo Success! Website opened in Chrome.
) else (
    echo Chrome not found in common locations.
    echo Opening in default browser instead...
    start "" "%HTML_PATH%"
    echo.
    echo Website opened in default browser.
    echo For best experience, please install Google Chrome.
)

echo.
echo ======================================================
echo    Website launched successfully! You can close this window.
echo ======================================================
echo.
timeout /t 5
