import { motion } from 'framer-motion'

const CompanyHistory = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-primary mb-6">Our Story</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-secondary mb-3">Foundation & Vision</h3>
                <p className="text-gray-700 leading-relaxed">
                  Anuradha Minerals and Metals was founded with a clear vision to bridge the gap between 
                  raw material suppliers and steel manufacturing facilities. Recognizing the critical need 
                  for reliable, high-quality materials in the steel industry, we established our operations 
                  to serve this specialized market.
                </p>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold text-secondary mb-3">Growth & Expansion</h3>
                <p className="text-gray-700 leading-relaxed">
                  From our initial focus on local steel plants, we have expanded our reach to serve 
                  major steel manufacturing facilities across 15+ countries. Our growth has been driven 
                  by our commitment to understanding the unique requirements of each steel plant and 
                  delivering materials that meet their exact specifications.
                </p>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold text-secondary mb-3">Industry Expertise</h3>
                <p className="text-gray-700 leading-relaxed">
                  Today, AMM stands as a trusted partner to blast furnace operators, electric arc furnace 
                  facilities, and steel rolling mills worldwide. Our deep understanding of metallurgical 
                  processes and steel production requirements sets us apart in the industry.
                </p>
              </div>
            </div>
          </motion.div>

          {/* Timeline */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="space-y-8">
              {[
                { year: '2010', event: 'Company Founded', desc: 'AMM established to serve steel industry' },
                { year: '2015', event: 'International Expansion', desc: 'First international steel plant partnerships' },
                { year: '2018', event: 'Quality Certifications', desc: 'Achieved steel industry quality standards' },
                { year: '2020', event: 'Technology Integration', desc: 'Advanced testing and quality control systems' },
                { year: '2024', event: 'Global Presence', desc: 'Serving 50+ steel plants in 15+ countries' }
              ].map((milestone, index) => (
                <div key={milestone.year} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center font-bold">
                    {milestone.year}
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-primary">{milestone.event}</h4>
                    <p className="text-gray-600">{milestone.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default CompanyHistory
