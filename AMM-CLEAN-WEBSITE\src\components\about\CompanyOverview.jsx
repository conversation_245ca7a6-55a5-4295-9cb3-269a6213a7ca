import { motion } from 'framer-motion'

const CompanyOverview = () => {
  return (
    <section className="section">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-primary mb-6">Our Company</h2>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Anuradha Minerals and Metals (AMM) was established with a vision to become 
              a leading player in the minerals and metals trading industry. Under the 
              leadership of Managing Director <PERSON><PERSON><PERSON>, we have built a reputation 
              for excellence and reliability.
            </p>
            <p className="text-gray-600 leading-relaxed">
              With a commitment to excellence and customer satisfaction, we have 
              quickly established ourselves as a trusted partner in the minerals 
              and metals trading industry.
            </p>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="bg-primary/5 p-8 rounded-lg"
          >
            <h3 className="text-xl font-semibold text-secondary mb-4">Managing Director</h3>
            <p className="text-gray-700 mb-2"><strong>Anuradha Singh</strong></p>
            <p className="text-gray-600 text-sm">
              Leading AMM with vision and dedication to provide exceptional 
              quality products and services to our valued customers.
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default CompanyOverview
