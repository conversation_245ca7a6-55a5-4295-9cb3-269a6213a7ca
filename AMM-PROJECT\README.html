<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AMM Website - How to Run</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #0a0f2c;
            border-bottom: 2px solid #c9a664;
            padding-bottom: 10px;
        }
        h2 {
            color: #0a0f2c;
            margin-top: 30px;
        }
        .method {
            background-color: #f5f0e1;
            border: 1px solid #c9a664;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .method h3 {
            margin-top: 0;
            color: #0a0f2c;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: Consolas, monospace;
        }
        .button {
            display: inline-block;
            background-color: #0a0f2c;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 10px;
            font-weight: bold;
        }
        .button:hover {
            background-color: #161d42;
        }
    </style>
</head>
<body>
    <h1>Anuradha Minerals and Metals Website</h1>
    
    <p>Welcome to the AMM Website! This page provides instructions on how to run the website on your computer.</p>
    
    <h2>How to Run the Website in Chrome</h2>
    
    <div class="method">
        <h3>Method 1: Use the Chrome Shortcut</h3>
        <p>Double-click the <code>Chrome-Shortcut.vbs</code> file in the main folder. This will automatically open the website in Chrome.</p>
        <a href="Chrome-Shortcut.vbs" class="button">Open in Chrome</a>
    </div>
    
    <div class="method">
        <h3>Method 2: Use the Batch File</h3>
        <p>Double-click the <code>open-in-chrome.bat</code> file in the main folder. This will also open the website in Chrome.</p>
        <a href="open-in-chrome.bat" class="button">Run Batch File</a>
    </div>
    
    <div class="method">
        <h3>Method 3: Open Directly</h3>
        <p>Navigate to the <code>src</code> folder and open <code>index.html</code> in Chrome:</p>
        <ol>
            <li>Open the <code>src</code> folder</li>
            <li>Right-click on <code>index.html</code></li>
            <li>Select "Open with" > "Google Chrome"</li>
        </ol>
        <a href="src/index.html" class="button">Open HTML File</a>
    </div>
    
    <h2>Folder Structure</h2>
    <p>The website is organized as follows:</p>
    <ul>
        <li><code>src/</code> - Contains all website files
            <ul>
                <li><code>*.html</code> - HTML files</li>
                <li><code>*.css</code> - CSS files</li>
                <li><code>*.js</code> - JavaScript files</li>
                <li><code>images/</code> - Image files</li>
            </ul>
        </li>
        <li><code>Chrome-Shortcut.vbs</code> - Script to open the website in Chrome</li>
        <li><code>open-in-chrome.bat</code> - Batch file to open the website in Chrome</li>
        <li><code>README.html</code> - This help file</li>
    </ul>
    
    <p><strong>Note:</strong> If Chrome is not installed in a standard location, the shortcuts will fall back to opening the website in your default browser.</p>
</body>
</html>
