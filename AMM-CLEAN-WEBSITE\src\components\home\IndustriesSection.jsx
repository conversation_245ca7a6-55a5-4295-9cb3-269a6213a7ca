import { motion } from 'framer-motion'

const IndustriesSection = () => {
  const industries = [
    {
      name: 'Steel Manufacturing',
      description: 'Supplying high-quality materials for steel plants and blast furnaces worldwide.',
      image: 'https://images.unsplash.com/photo-1565793298595-6a879b1d9492?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      icon: '🏭',
      applications: ['Blast Furnaces', 'Steel Mills', 'Rolling Mills', 'Foundries']
    },
    {
      name: 'Automotive Industry',
      description: 'Premium aluminium and metals for automotive manufacturing and components.',
      image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      icon: '🚗',
      applications: ['Engine Components', 'Body Panels', 'Chassis Parts', 'Transmission']
    },
    {
      name: 'Aerospace & Defense',
      description: 'High-grade materials meeting stringent aerospace and defense specifications.',
      image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      icon: '✈️',
      applications: ['Aircraft Frames', 'Engine Parts', 'Landing Gear', 'Structural Components']
    },
    {
      name: 'Construction & Infrastructure',
      description: 'Reliable supply of construction materials for major infrastructure projects.',
      image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      icon: '🏗️',
      applications: ['Building Frames', 'Bridges', 'Infrastructure', 'Architectural Elements']
    },
    {
      name: 'Chemical Processing',
      description: 'Specialized materials for chemical plants and processing facilities.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      icon: '⚗️',
      applications: ['Reactors', 'Heat Exchangers', 'Pipelines', 'Storage Tanks']
    },
    {
      name: 'Energy & Power',
      description: 'Materials for power generation and renewable energy infrastructure.',
      image: 'https://images.unsplash.com/photo-1587293852726-70cdb56c2866?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      icon: '⚡',
      applications: ['Power Plants', 'Wind Turbines', 'Solar Panels', 'Transmission Lines']
    }
  ]

  return (
    <section className="section bg-gray-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <img
          src="https://images.unsplash.com/photo-1565793298595-6a879b1d9492?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
          alt="Industrial Background"
          className="w-full h-full object-cover"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-6">
            Industries We <span className="text-secondary">Serve</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            From blast furnaces to aerospace manufacturing, we supply premium materials 
            across diverse industrial sectors worldwide.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {industries.map((industry, index) => (
            <motion.div
              key={industry.name}
              className="group relative bg-white/5 backdrop-blur-sm rounded-lg overflow-hidden border border-secondary/20 hover:border-secondary/50 transition-all duration-500"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              {/* Industry Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={industry.image}
                  alt={industry.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-gray-900/90 via-gray-900/50 to-transparent" />
                <div className="absolute top-4 right-4 text-3xl bg-secondary/20 backdrop-blur-sm rounded-full p-3">
                  {industry.icon}
                </div>
                <div className="absolute bottom-4 left-4 right-4">
                  <h3 className="text-xl font-bold text-white mb-2">{industry.name}</h3>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <p className="text-gray-300 mb-4 leading-relaxed">{industry.description}</p>
                
                <div>
                  <h4 className="font-semibold text-secondary mb-3">Key Applications:</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {industry.applications.map((app, idx) => (
                      <div
                        key={idx}
                        className="text-sm text-gray-400 bg-white/5 rounded px-2 py-1 border border-secondary/10"
                      >
                        {app}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-secondary/10 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-secondary/10 backdrop-blur-sm border border-secondary/30 rounded-lg p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-secondary mb-4">
              Partner with Industry Leaders
            </h3>
            <p className="text-gray-300 mb-6">
              Join the ranks of satisfied customers who trust AMM for their critical material needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-secondary hover:bg-secondary-light text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300">
                Request Quote
              </button>
              <button className="bg-primary hover:bg-primary-light text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300">
                View Products
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default IndustriesSection
