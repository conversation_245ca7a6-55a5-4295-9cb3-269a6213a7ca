import { motion } from 'framer-motion'

const ProductsList = () => {
  const products = [
    {
      name: 'Aluminium Ingots',
      description: 'High-purity aluminium ingots suitable for various industrial applications including automotive, aerospace, and construction industries.',
      features: ['99.7% purity', 'Various sizes available', 'Certified quality', 'Competitive pricing'],
      applications: ['Automotive parts', 'Aerospace components', 'Construction materials', 'Electronics'],
      image: 'https://images.unsplash.com/photo-1587293852726-70cdb56c2866?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      industrialImages: [
        'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
      ],
      icon: '🔩'
    },
    {
      name: 'Calcined Lime',
      description: 'Premium quality calcined lime for steel production, water treatment, and chemical processing applications.',
      features: ['High calcium content', 'Low impurities', 'Consistent quality', 'Reliable supply'],
      applications: ['Steel production', 'Water treatment', 'Chemical processing', 'Construction'],
      image: 'https://images.unsplash.com/photo-1565793298595-6a879b1d9492?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      industrialImages: [
        'https://images.unsplash.com/photo-1565793298595-6a879b1d9492?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
      ],
      icon: '⚪'
    },
    {
      name: 'Glycerin',
      description: 'Pure glycerin for pharmaceutical, cosmetic, and food industry applications.',
      features: ['USP grade available', 'Food grade quality', 'Pharmaceutical grade', 'High purity'],
      applications: ['Pharmaceuticals', 'Cosmetics', 'Food industry', 'Personal care'],
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      industrialImages: [
        'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
      ],
      icon: '💧'
    },
    {
      name: 'Scrap Materials',
      description: 'High-quality ferrous and non-ferrous scrap materials for recycling and reprocessing.',
      features: ['Sorted and graded', 'Competitive pricing', 'Reliable supply', 'Quality assured'],
      applications: ['Steel mills', 'Foundries', 'Recycling plants', 'Manufacturing'],
      image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      industrialImages: [
        'https://images.unsplash.com/photo-1565793298595-6a879b1d9492?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1587293852726-70cdb56c2866?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
      ],
      icon: '♻️'
    }
  ]

  return (
    <section className="section">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {products.map((product, index) => (
            <motion.div
              key={product.name}
              className="bg-white rounded-lg shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden group"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              {/* Product Header with Image */}
              <div className="relative h-64 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/90 via-primary/50 to-transparent" />
                <div className="absolute top-4 right-4 text-4xl bg-white/20 backdrop-blur-sm rounded-full p-3">
                  {product.icon}
                </div>
                <div className="absolute bottom-4 left-4 right-4">
                  <h2 className="text-2xl font-bold text-white mb-2">{product.name}</h2>
                  <p className="text-gray-200 text-sm">{product.description}</p>
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-secondary mb-3">Key Features:</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {product.features.map((feature, idx) => (
                      <div
                        key={idx}
                        className="text-sm text-gray-600 bg-gray-50 rounded px-3 py-2 border border-gray-200"
                      >
                        • {feature}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-secondary mb-3">Applications:</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {product.applications.map((application, idx) => (
                      <div
                        key={idx}
                        className="text-sm text-gray-600 bg-secondary/5 rounded px-3 py-2 border border-secondary/20"
                      >
                        • {application}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Industrial Applications Gallery */}
                <div>
                  <h3 className="text-lg font-semibold text-secondary mb-3">Industrial Applications:</h3>
                  <div className="grid grid-cols-2 gap-3">
                    {product.industrialImages.map((imgSrc, idx) => (
                      <div key={idx} className="relative h-24 rounded-lg overflow-hidden">
                        <img
                          src={imgSrc}
                          alt={`${product.name} Industrial Application ${idx + 1}`}
                          className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-primary/60 to-transparent" />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default ProductsList
