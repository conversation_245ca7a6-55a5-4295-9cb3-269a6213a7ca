import { motion } from 'framer-motion'

const ProductsList = () => {
  const products = [
    {
      name: 'Aluminium Ingots',
      description: 'High-purity aluminium ingots suitable for various industrial applications including automotive, aerospace, and construction industries.',
      features: ['99.7% purity', 'Various sizes available', 'Certified quality', 'Competitive pricing'],
      applications: ['Automotive parts', 'Aerospace components', 'Construction materials', 'Electronics'],
      icon: '🔩'
    },
    {
      name: 'Calcined Lime',
      description: 'Premium quality calcined lime for steel production, water treatment, and chemical processing applications.',
      features: ['High calcium content', 'Low impurities', 'Consistent quality', 'Reliable supply'],
      applications: ['Steel production', 'Water treatment', 'Chemical processing', 'Construction'],
      icon: '⚪'
    },
    {
      name: 'Glycerin',
      description: 'Pure glycerin for pharmaceutical, cosmetic, and food industry applications.',
      features: ['USP grade available', 'Food grade quality', 'Pharmaceutical grade', 'High purity'],
      applications: ['Pharmaceuticals', 'Cosmetics', 'Food industry', 'Personal care'],
      icon: '💧'
    },
    {
      name: 'Scrap Materials',
      description: 'High-quality ferrous and non-ferrous scrap materials for recycling and reprocessing.',
      features: ['Sorted and graded', 'Competitive pricing', 'Reliable supply', 'Quality assured'],
      applications: ['Steel mills', 'Foundries', 'Recycling plants', 'Manufacturing'],
      icon: '♻️'
    }
  ]

  return (
    <section className="section">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {products.map((product, index) => (
            <motion.div
              key={product.name}
              className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center mb-6">
                <div className="text-4xl mr-4">{product.icon}</div>
                <h2 className="text-2xl font-bold text-primary">{product.name}</h2>
              </div>
              
              <p className="text-gray-600 mb-6">{product.description}</p>
              
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-secondary mb-3">Key Features:</h3>
                <ul className="text-gray-600 space-y-1">
                  {product.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center">
                      <span className="text-secondary mr-2">•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-secondary mb-3">Applications:</h3>
                <ul className="text-gray-600 space-y-1">
                  {product.applications.map((application, idx) => (
                    <li key={idx} className="flex items-center">
                      <span className="text-secondary mr-2">•</span>
                      {application}
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default ProductsList
