/* Base styles */
:root {
    --primary: #1a1a1a;
    --primary-light: #2a2a2a;
    --primary-dark: #111111;
    --secondary: #c9a664;
    --secondary-light: #d9bf8c;
    --secondary-dark: #b08d3c;
    --accent: #c9a664;
    --accent-light: #d9bf8c;
    --accent-dark: #b08d3c;
    --dark: #333333;
    --light: #f5f5f5;
    --text-light: #f9f9f9;
    --text-secondary: #cccccc;
    --text-dark: #333333;
    --cream: #f5f0e1;
    --cream-dark: #e8dcc8;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--cream);
    overflow-x: hidden;
    position: relative;
    letter-spacing: 0.2px;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    margin-bottom: 1rem;
    letter-spacing: 0.5px;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Futuristic Background */
.futuristic-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--cream);
    overflow: hidden;
    perspective: 1500px;
}

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(to right, rgba(201, 166, 100, 0.03) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(201, 166, 100, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.5;
    transform: rotateX(60deg) scale(3);
    transform-style: preserve-3d;
    transform-origin: center center;
    animation: gridMove 40s infinite linear;
}

/* Add a second grid for more depth */
.grid-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(to right, rgba(201, 166, 100, 0.05) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(201, 166, 100, 0.05) 1px, transparent 1px);
    background-size: 100px 100px;
    opacity: 0.4;
    transform: translateZ(-50px);
    animation: gridMove 60s infinite linear reverse;
}

@keyframes gridMove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 60px 60px;
    }
}

.glow-effect {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    transform-style: preserve-3d;
}

.glow-1 {
    top: 20%;
    left: 20%;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(201, 166, 100, 0.15) 0%, rgba(201, 166, 100, 0) 70%);
    animation: pulse 12s infinite alternate, float3D 25s infinite ease-in-out;
    transform: translateZ(60px);
}

.glow-2 {
    bottom: 20%;
    right: 20%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(201, 166, 100, 0.08) 0%, rgba(201, 166, 100, 0) 70%);
    animation: pulse 15s infinite alternate-reverse, float3D 20s infinite ease-in-out reverse;
    transform: translateZ(40px);
}

/* Add a third glow for accent color */
.glow-effect.glow-3 {
    top: 60%;
    left: 60%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(201, 166, 100, 0.2) 0%, rgba(201, 166, 100, 0) 70%);
    animation: pulse 10s infinite alternate, float3D 18s infinite ease-in-out;
    transform: translateZ(30px);
}

.light-beam {
    position: absolute;
    height: 1px;
    width: 100%;
    top: 50%;
    left: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(201, 166, 100, 0.3) 50%, transparent 100%);
    box-shadow: 0 0 25px 3px rgba(201, 166, 100, 0.2);
    opacity: 0.5;
    transform: translateZ(25px) rotateX(80deg);
    transform-style: preserve-3d;
}

/* Add a second light beam with gold color */
.light-beam::after {
    content: '';
    position: absolute;
    height: 1px;
    width: 100%;
    top: 100px;
    left: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(201, 166, 100, 0.6) 50%, transparent 100%);
    box-shadow: 0 0 20px 2px rgba(201, 166, 100, 0.4);
    opacity: 0.6;
    transform: translateZ(-20px);
}

.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
}

.particles::before {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(201, 166, 100, 0.8);
    box-shadow: 0 0 10px 2px rgba(201, 166, 100, 0.6);
    border-radius: 50%;
    top: 20%;
    left: 30%;
    animation: float3D 15s infinite linear;
    transform: translateZ(40px);
}

.particles::after {
    content: '';
    position: absolute;
    width: 3px;
    height: 3px;
    background: rgba(201, 166, 100, 0.8);
    box-shadow: 0 0 8px 2px rgba(201, 166, 100, 0.6);
    border-radius: 50%;
    top: 70%;
    left: 60%;
    animation: float3D 20s infinite linear reverse;
    transform: translateZ(60px);
}

/* Add more particles for better effect */
.futuristic-background::before {
    content: '';
    position: absolute;
    width: 5px;
    height: 5px;
    background: rgba(201, 166, 100, 0.9);
    box-shadow: 0 0 15px 3px rgba(201, 166, 100, 0.7);
    border-radius: 50%;
    top: 40%;
    left: 70%;
    animation: float3D 18s infinite linear;
    transform: translateZ(70px);
}

.futuristic-background::after {
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(201, 166, 100, 0.8);
    box-shadow: 0 0 20px 4px rgba(201, 166, 100, 0.6);
    border-radius: 50%;
    top: 80%;
    left: 20%;
    animation: float3D 25s infinite linear reverse;
    transform: translateZ(90px);
}

@keyframes float3D {
    0% {
        transform: translate3d(0, 0, 40px);
    }
    25% {
        transform: translate3d(50px, -30px, 60px);
    }
    50% {
        transform: translate3d(100px, 0, 80px);
    }
    75% {
        transform: translate3d(50px, 30px, 60px);
    }
    100% {
        transform: translate3d(0, 0, 40px);
    }
}

/* Header */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    padding: 1.2rem 0;
    backdrop-filter: blur(20px);
    background: rgba(26, 26, 26, 0.9);
    border-bottom: 1px solid rgba(201, 166, 100, 0.3);
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.25);
    transform-style: preserve-3d;
    transition: all 0.4s ease;
}

header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(201, 166, 100, 0.6), transparent);
    opacity: 0.7;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    position: relative;
    display: inline-block;
    transform-style: preserve-3d;
    transition: transform 0.5s ease;
}

.logo:hover {
    transform: scale(1.05) translateZ(10px);
}

.logo-image {
    height: 40px;
    width: auto;
    transform: translateZ(10px);
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.4));
    transition: all 0.3s ease;
    margin-top: 5px;
}

.logo:hover .logo-image {
    filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.6));
    transform: translateZ(15px) scale(1.05);
}

.logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 140%;
    height: 140%;
    background: radial-gradient(circle, rgba(201, 166, 100, 0.7) 0%, transparent 70%);
    filter: blur(20px);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.logo:hover .logo-glow {
    opacity: 1;
    animation: pulse 2s infinite alternate;
}

.nav-links {
    display: flex;
    gap: 2.5rem;
}

.nav-links a {
    position: relative;
    font-weight: 500;
    padding: 0.5rem 0;
    margin: 0 0.2rem;
    color: var(--text-light);
    letter-spacing: 0.8px;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transform-style: preserve-3d;
}

.nav-links a:hover {
    color: var(--secondary);
    transform: translateZ(8px);
}

.nav-links a::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    width: 0;
    height: 1px;
    background: var(--secondary);
    transform: translateX(-50%);
    transition: width 0.3s ease;
    opacity: 0;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--secondary), transparent);
    transition: width 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    box-shadow: 0 0 10px rgba(201, 166, 100, 0.8);
}

.nav-links a:hover::before,
.nav-links a.active::before {
    width: 10px;
    opacity: 1;
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.nav-links a.active {
    color: var(--secondary);
    font-weight: 600;
    transform: translateZ(5px);
}

.menu-toggle {
    display: none;
    flex-direction: column;
    gap: 5px;
    cursor: pointer;
    z-index: 101;
}

.bar {
    width: 28px;
    height: 3px;
    background-color: var(--secondary);
    transition: var(--transition);
    border-radius: 2px;
    box-shadow: 0 0 5px rgba(226, 201, 146, 0.5);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 6rem 0 3rem;
    overflow: hidden;
    perspective: 1500px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(201, 166, 100, 0.05) 0%, rgba(201, 166, 100, 0) 70%);
    z-index: -1;
}

.hero .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    width: 100%;
}

.hero-content {
    max-width: 800px;
    transform-style: preserve-3d;
    animation: fadeInUp 1.2s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
    position: relative;
    z-index: 2;
    text-align: center;
}

.glitch-text {
    position: relative;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(to right, var(--primary-dark), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(10, 15, 44, 0.6);
    transform: translateZ(15px);
    transform-style: preserve-3d;
    letter-spacing: 2px;
    line-height: 1.2;
}

.glitch-text::before,
.glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, var(--primary-dark), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glitch-text::before {
    left: 2px;
    text-shadow: -2px 0 rgba(201, 166, 100, 0.7);
    animation: glitch-anim-1 4s infinite linear alternate-reverse;
    opacity: 0.2;
    transform: translateZ(5px);
}

.glitch-text::after {
    left: -2px;
    text-shadow: 2px 0 rgba(201, 166, 100, 0.7);
    animation: glitch-anim-2 5s infinite linear alternate-reverse;
    opacity: 0.2;
    transform: translateZ(10px);
}

/* Add a glow effect to the heading */
.glitch-text::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary);
    filter: blur(40px);
    opacity: 0.1;
    z-index: -1;
    transform: translateZ(-5px);
}

.subtitle {
    font-size: 1.8rem;
    font-weight: 500;
    margin-bottom: 1.2rem;
    color: var(--primary);
    text-shadow: 0 0 12px rgba(10, 15, 44, 0.3);
    transform: translateZ(8px);
    letter-spacing: 1px;
    font-family: 'Rajdhani', sans-serif;
    position: relative;
    display: inline-block;
}

.subtitle::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(to right, var(--secondary), transparent);
    box-shadow: 0 0 10px rgba(201, 166, 100, 0.5);
}

.description {
    font-size: 1.2rem;
    margin-bottom: 2.8rem;
    color: var(--text-dark);
    opacity: 0.95;
    line-height: 1.8;
    transform: translateZ(5px);
    max-width: 90%;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    transform-style: preserve-3d;
}

.btn {
    position: relative;
    display: inline-block;
    padding: 1rem 2.5rem;
    border-radius: 4px;
    font-weight: 600;
    overflow: hidden;
    z-index: 1;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transform-style: preserve-3d;
    letter-spacing: 1px;
    font-family: 'Rajdhani', sans-serif;
    text-transform: uppercase;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s;
    z-index: -1;
}

.btn:hover::before {
    transform: translateX(100%);
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-dark), var(--secondary));
    color: var(--primary-dark);
    border: 1px solid rgba(201, 166, 100, 0.6);
    box-shadow: 0 5px 15px rgba(201, 166, 100, 0.3), inset 0 1px 1px rgba(255, 255, 255, 0.2);
    transform: translateZ(12px);
}

.btn-secondary {
    background: rgba(10, 15, 44, 0.3);
    color: var(--cream);
    border: 1px solid rgba(201, 166, 100, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), inset 0 1px 1px rgba(255, 255, 255, 0.1);
    transform: translateZ(8px);
}

.btn-outline {
    background: transparent;
    color: var(--cream);
    border: 1px solid rgba(201, 166, 100, 0.5);
    transform: translateZ(8px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(201, 166, 100, 0.7) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.btn:hover {
    transform: translateY(-5px) translateZ(20px);
}

.btn:hover .btn-glow {
    opacity: 0.7;
    animation: pulse 2s infinite alternate;
}

.btn-primary:hover {
    box-shadow: 0 10px 25px rgba(201, 166, 100, 0.5), 0 0 20px rgba(201, 166, 100, 0.6);
    background: linear-gradient(135deg, var(--secondary), var(--secondary-light));
}

.btn-secondary:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(201, 166, 100, 0.4);
    border-color: var(--secondary);
    background: rgba(10, 15, 44, 0.5);
}

.btn-outline:hover {
    box-shadow: 0 0 25px rgba(201, 166, 100, 0.4);
    border-color: var(--secondary);
    background: rgba(201, 166, 100, 0.1);
}

.hero-card {
    position: relative;
    width: 280px;
    height: 400px;
    perspective: 2000px;
    transform-style: preserve-3d;
    animation: float 10s infinite ease-in-out;
    z-index: 2;
    margin-top: 1rem;
    margin-bottom: 2rem;
}

.card-content {
    position: relative;
    padding: 2.5rem;
    background: linear-gradient(145deg, rgba(10, 15, 44, 0.9), rgba(20, 29, 74, 0.95));
    border-radius: 6px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(201, 166, 100, 0.2);
    border: 1px solid rgba(201, 166, 100, 0.3);
    backdrop-filter: blur(20px);
    transform-style: preserve-3d;
    transform: translateZ(50px) rotateX(5deg) rotateY(-5deg);
    transition: transform 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow: hidden;
    color: var(--text-light);
}

.card-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent, rgba(201, 166, 100, 0.05), transparent);
    z-index: -1;
}

.card-content::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, rgba(201, 166, 100, 0.1) 0%, transparent 60%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: -1;
}

.card-content:hover {
    transform: translateZ(70px) rotateX(8deg) rotateY(-8deg);
    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.5), 0 0 40px rgba(201, 166, 100, 0.3);
    border-color: rgba(201, 166, 100, 0.5);
}

.card-content:hover::after {
    opacity: 1;
}

.card-header {
    text-align: center;
    margin-bottom: 2.5rem;
    transform: translateZ(25px);
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--secondary), transparent);
    box-shadow: 0 0 10px rgba(201, 166, 100, 0.5);
}

.card-header h3 {
    font-family: 'Orbitron', sans-serif;
    font-size: 2.8rem;
    background: linear-gradient(to right, var(--cream), var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 3px 15px rgba(201, 166, 100, 0.5);
    letter-spacing: 3px;
    position: relative;
    display: inline-block;
}

.card-header h3::after {
    content: 'AMM';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, var(--cream), var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 0.3;
    filter: blur(4px);
    transform: translateZ(-5px);
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 1.4rem;
    transform: translateZ(15px);
    position: relative;
}

.feature-list::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    border: 1px solid rgba(201, 166, 100, 0.1);
    border-radius: 4px;
    z-index: -1;
    opacity: 0.5;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    padding: 0.5rem 0;
    position: relative;
}

.feature-list li::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background: linear-gradient(to right, var(--secondary), transparent);
    transition: width 0.4s ease;
    opacity: 0.3;
}

.feature-list li:hover {
    transform: translateX(8px) translateZ(8px);
}

.feature-list li:hover::after {
    width: 100%;
}

.feature-dot {
    display: block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--secondary);
    box-shadow: 0 0 12px rgba(201, 166, 100, 0.8);
    position: relative;
}

.feature-dot::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 1px solid rgba(201, 166, 100, 0.3);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.feature-list li:hover .feature-dot::after {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
}

.card-footer {
    margin-top: 2.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(201, 166, 100, 0.2);
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--cream);
    transform: translateZ(10px);
    position: relative;
}

.card-footer::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(201, 166, 100, 0.5), transparent);
    opacity: 0.5;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    transform-style: preserve-3d;
    z-index: 1;
}

.floating-element {
    position: absolute;
    border: 1px solid rgba(201, 166, 100, 0.3);
    box-shadow: 0 0 25px rgba(201, 166, 100, 0.2);
    transform-style: preserve-3d;
    backdrop-filter: blur(5px);
    background: rgba(201, 166, 100, 0.05);
}

/* Circular element */
.element-1 {
    top: 15%;
    right: 8%;
    width: 140px;
    height: 140px;
    border-radius: 50%;
    animation: float3D 12s infinite ease-in-out;
    transform: translateZ(100px);
    border-width: 2px;
    border-style: dashed;
    border-color: rgba(201, 166, 100, 0.2);
    position: relative;
}

.element-1::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70%;
    height: 70%;
    border: 1px solid rgba(201, 166, 100, 0.2);
    border-radius: 50%;
    animation: pulse 8s infinite alternate;
}

/* Square element */
.element-2 {
    bottom: 25%;
    left: 12%;
    width: 120px;
    height: 120px;
    transform: rotate(45deg) translateZ(80px);
    animation: float3D 15s infinite ease-in-out reverse;
    position: relative;
    overflow: hidden;
}

.element-2::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(201, 166, 100, 0.1), transparent);
    animation: rotateGradient 8s infinite linear;
}

.element-2::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 1px solid rgba(201, 166, 100, 0.2);
    transform: rotate(15deg);
}

/* Rounded square element */
.element-3 {
    top: 55%;
    right: 15%;
    width: 90px;
    height: 90px;
    border-radius: 12px;
    animation: float3D 18s infinite ease-in-out;
    transform: translateZ(60px);
    position: relative;
    overflow: hidden;
}

.element-3::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent, rgba(0, 170, 255, 0.1), transparent);
    animation: pulse 10s infinite alternate;
}

/* Add a fourth element for more visual interest */
.floating-element.element-4 {
    top: 70%;
    left: 25%;
    width: 60px;
    height: 60px;
    border-radius: 4px;
    animation: float3D 20s infinite ease-in-out;
    transform: translateZ(40px) rotate(30deg);
    border-color: rgba(0, 170, 255, 0.2);
}

@keyframes rotateGradient {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.hero-bottom {
    position: absolute;
    bottom: 2.5rem;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    transform-style: preserve-3d;
    z-index: 2;
}

.product-tags {
    display: flex;
    gap: 3rem;
    transform-style: preserve-3d;
}

.product-tag {
    position: relative;
    padding: 0.7rem 1.8rem;
    background: rgba(10, 15, 44, 0.5);
    border: 1px solid rgba(201, 166, 100, 0.3);
    border-radius: 4px;
    font-size: 0.9rem;
    color: var(--cream);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transform: translateZ(25px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    font-family: 'Rajdhani', sans-serif;
    letter-spacing: 1px;
    text-transform: uppercase;
    overflow: hidden;
}

.product-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(201, 166, 100, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s;
    z-index: -1;
}

.product-tag::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--secondary);
    transition: width 0.4s ease;
}

.product-tag:hover {
    transform: translateY(-8px) translateZ(35px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(201, 166, 100, 0.3);
    border-color: rgba(201, 166, 100, 0.5);
    background: rgba(10, 15, 44, 0.7);
    color: var(--secondary);
}

.product-tag:hover::before {
    transform: translateX(100%);
}

.product-tag:hover::after {
    width: 100%;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Products Section */
.products {
    padding: 6rem 0;
    background: linear-gradient(to bottom, rgba(9, 37, 69, 0.8), rgba(15, 52, 96, 0.8));
    position: relative;
    z-index: 1;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    background: linear-gradient(to right, var(--primary-dark), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
    margin-bottom: 1rem;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.product-card {
    position: relative;
    background: linear-gradient(145deg, rgba(10, 15, 44, 0.85), rgba(6, 10, 30, 0.9));
    border: 1px solid rgba(201, 166, 100, 0.25);
    border-radius: 4px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    transform-style: preserve-3d;
    color: var(--text-light);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(201, 166, 100, 0.05), transparent 70%);
    z-index: 0;
}

.product-card:hover {
    transform: translateY(-8px) translateZ(10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 0 15px rgba(201, 166, 100, 0.2);
    border-color: rgba(201, 166, 100, 0.4);
}

.product-image {
    height: 180px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid rgba(201, 166, 100, 0.2);
}

.product-image::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(to top, rgba(6, 10, 30, 0.8), transparent);
}

.glycine-image, .scrap-image, .aluminium-image, .lime-image {
    position: relative;
    background-size: cover;
    background-position: center;
    overflow: hidden;
}

.glycine-image {
    background-image: url('images/real-products/glycine.svg');
    background-color: #2c3e50;
}

.scrap-image {
    background-image: url('images/real-products/soda-ash.svg');
    background-color: #2c3e50;
}

.aluminium-image {
    background-image: url('images/real-products/aluminium-ingots.svg');
    background-color: #2c3e50;
}

.lime-image {
    background-image: url('images/real-products/calcined-lime.svg');
    background-color: #2c3e50;
}

.product-card:hover .glycine-image,
.product-card:hover .scrap-image,
.product-card:hover .aluminium-image,
.product-card:hover .lime-image {
    background-size: 110% auto;
}

.product-info {
    padding: 1.2rem;
    position: relative;
    z-index: 1;
}

.product-info h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--secondary);
    letter-spacing: 0.5px;
}

.product-info p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--cream);
    margin-bottom: 0.8rem;
}

.btn-text {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    font-weight: 500;
    color: var(--secondary);
    transition: var(--transition);
}

.btn-text i {
    transition: transform 0.3s ease;
}

.btn-text:hover {
    color: var(--secondary-light);
}

.btn-text:hover i {
    transform: translateX(5px);
}

/* About Preview Section */
.about-preview {
    padding: 6rem 0;
    background: linear-gradient(to bottom, rgba(10, 15, 44, 0.9), rgba(6, 10, 30, 0.95));
    position: relative;
    z-index: 1;
    color: var(--text-light);
}

.about-content {
    display: flex;
    align-items: center;
    gap: 4rem;
}

.about-text {
    flex: 1;
}

.about-text h2 {
    font-size: 2.5rem;
    background: linear-gradient(to right, #ffffff, var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
}

.director-info {
    margin-bottom: 1.5rem;
}

.director-info h3 {
    font-size: 1.8rem;
    color: var(--secondary);
    margin-bottom: 0.2rem;
}

.director-title {
    font-size: 1rem;
    opacity: 0.8;
}

.quote {
    font-style: italic;
    margin-bottom: 2rem;
    padding-left: 1rem;
    border-left: 3px solid var(--secondary);
}

.about-image {
    flex: 1;
    display: flex;
    justify-content: center;
}

.image-frame {
    position: relative;
    width: 300px;
    height: 400px;
    border: 1px solid rgba(201, 166, 100, 0.3);
    border-radius: 10px;
    overflow: hidden;
    transform-style: preserve-3d;
    transform: perspective(1000px) rotateY(-5deg);
    transition: var(--transition);
}

.image-frame:hover {
    transform: perspective(1000px) rotateY(-8deg);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 20px rgba(201, 166, 100, 0.2);
}

.director-image {
    width: 100%;
    height: 100%;
    background: rgba(9, 37, 69, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-size: 5rem;
}

.director-image span {
    font-size: 1.2rem;
    color: var(--secondary);
}

/* CTA Section */
.cta {
    padding: 6rem 0;
    background: linear-gradient(145deg, rgba(10, 15, 44, 0.9), rgba(6, 10, 30, 0.95));
    position: relative;
    z-index: 1;
    color: var(--text-light);
    border-top: 1px solid rgba(201, 166, 100, 0.2);
    border-bottom: 1px solid rgba(201, 166, 100, 0.2);
}

.cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(201, 166, 100, 0.05), transparent 70%);
    z-index: -1;
}

.cta-content {
    max-width: 700px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 1;
}

.cta-content h2 {
    font-size: 2.5rem;
    background: linear-gradient(to right, var(--cream), var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    font-family: 'Rajdhani', sans-serif;
    letter-spacing: 1px;
}

.cta-content p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.95;
    color: var(--cream);
    line-height: 1.6;
}

/* Footer */
footer {
    background: linear-gradient(to bottom, rgba(10, 15, 44, 0.95), rgba(6, 10, 30, 0.95));
    padding: 5rem 0 2rem;
    border-top: 1px solid rgba(201, 166, 100, 0.2);
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 10;
    transform-style: preserve-3d;
}

/* Add subtle grid pattern to footer */
footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(to right, rgba(201, 166, 100, 0.03) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(201, 166, 100, 0.03) 1px, transparent 1px);
    background-size: 30px 30px;
    z-index: -1;
}

/* Add decorative top border */
footer::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(201, 166, 100, 0.5), transparent);
    box-shadow: 0 0 15px rgba(201, 166, 100, 0.3);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 3.5rem;
    margin-bottom: 4rem;
    transform-style: preserve-3d;
}

.footer-column {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

.footer-column:hover {
    transform: translateZ(10px);
}

.footer-column h3 {
    font-size: 1.4rem;
    font-family: 'Rajdhani', sans-serif;
    color: var(--secondary);
    margin-bottom: 1.8rem;
    position: relative;
    padding-bottom: 0.8rem;
    letter-spacing: 0.5px;
    transform: translateZ(5px);
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(to right, var(--secondary), transparent);
    box-shadow: 0 0 10px rgba(226, 201, 146, 0.5);
}

.footer-column p {
    margin-bottom: 1.8rem;
    color: var(--cream);
    opacity: 0.9;
    font-size: 1rem;
    line-height: 1.6;
    transform: translateZ(3px);
}

.social-links {
    display: flex;
    gap: 1.2rem;
    transform-style: preserve-3d;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(201, 166, 100, 0.1);
    border: 1px solid rgba(226, 201, 146, 0.3);
    border-radius: 50%;
    font-size: 1.3rem;
    color: var(--cream);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateZ(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-links a:hover {
    background: rgba(226, 201, 146, 0.2);
    transform: translateY(-5px) translateZ(15px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2), 0 0 15px rgba(226, 201, 146, 0.3);
    color: var(--secondary);
    border-color: var(--secondary);
}

.footer-column ul {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    transform-style: preserve-3d;
}

.footer-column ul a {
    color: var(--cream);
    opacity: 0.9;
    transition: all 0.3s ease;
    position: relative;
    padding-left: 1.2rem;
    transform: translateZ(5px);
}

.footer-column ul a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background-color: var(--secondary);
    border-radius: 50%;
    box-shadow: 0 0 5px rgba(226, 201, 146, 0.5);
    transition: all 0.3s ease;
}

.footer-column ul a:hover {
    color: var(--secondary);
    opacity: 1;
    transform: translateX(5px) translateZ(10px);
}

.footer-column ul a:hover::before {
    background-color: var(--secondary-light);
    box-shadow: 0 0 8px rgba(226, 201, 146, 0.8);
}

.contact-info li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.2rem;
    color: var(--cream);
    opacity: 0.9;
    transform: translateZ(5px);
    transition: transform 0.3s ease;
}

.contact-info li:hover {
    transform: translateX(5px) translateZ(10px);
}

.contact-info i {
    color: var(--secondary);
    font-size: 1.2rem;
    width: 25px;
    text-align: center;
    text-shadow: 0 0 5px rgba(226, 201, 146, 0.5);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(226, 201, 146, 0.15);
    font-size: 0.95rem;
    color: var(--cream);
    opacity: 0.8;
    transform: translateZ(5px);
    position: relative;
}

/* Add decorative elements to footer */
.footer-bottom::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--secondary), transparent);
    box-shadow: 0 0 10px rgba(226, 201, 146, 0.5);
}

/* Commitment Items in Footer */
.commitment-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.commitment-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    transition: transform 0.3s ease;
    color: var(--cream);
    opacity: 0.9;
    transform: translateZ(5px);
}

.commitment-item:hover {
    transform: translateX(5px) translateZ(10px);
    opacity: 1;
}

.commitment-item i {
    color: var(--secondary);
    font-size: 1rem;
    text-shadow: 0 0 5px rgba(226, 201, 146, 0.5);
}

.commitment-item span {
    font-size: 0.95rem;
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.1);
    }
}

@keyframes glitch-anim-1 {
    0%, 100% {
        clip-path: inset(0 0 98% 0);
    }
    20% {
        clip-path: inset(33% 0 33% 0);
    }
    40% {
        clip-path: inset(50% 0 43% 0);
    }
    60% {
        clip-path: inset(25% 0 58% 0);
    }
    80% {
        clip-path: inset(75% 0 18% 0);
    }
}

@keyframes glitch-anim-2 {
    0%, 100% {
        clip-path: inset(0 0 78% 0);
    }
    20% {
        clip-path: inset(15% 0 49% 0);
    }
    40% {
        clip-path: inset(60% 0 24% 0);
    }
    60% {
        clip-path: inset(30% 0 35% 0);
    }
    80% {
        clip-path: inset(53% 0 10% 0);
    }
}

/* Why Choose Us Section */
.why-choose-us {
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
    background: linear-gradient(to bottom, var(--cream), var(--cream-dark));
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin-top: 3rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(201, 166, 100, 0.2);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    z-index: 1;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(201, 166, 100, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.feature-card:hover {
    transform: translateY(-10px) translateZ(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 20px rgba(201, 166, 100, 0.2);
    border-color: rgba(201, 166, 100, 0.4);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(145deg, var(--secondary-light), var(--secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: var(--primary-dark);
    font-size: 1.8rem;
    box-shadow: 0 10px 20px rgba(201, 166, 100, 0.3);
    position: relative;
    z-index: 1;
    transition: all 0.4s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) translateZ(15px);
    box-shadow: 0 15px 30px rgba(201, 166, 100, 0.4);
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.feature-card h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, var(--secondary), transparent);
    transition: width 0.4s ease;
}

.feature-card:hover h3::after {
    width: 100%;
}

.feature-card p {
    color: var(--text-dark);
    line-height: 1.7;
    opacity: 0.9;
}

/* Industry Solutions Section */
.industry-solutions {
    padding: 6rem 0;
    position: relative;
    background: var(--primary);
    color: var(--text-light);
    overflow: hidden;
}

.industry-solutions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(201, 166, 100, 0.1) 0%, rgba(201, 166, 100, 0) 70%);
    z-index: 0;
}

.industry-solutions .section-header h2 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffffff;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.industry-solutions .section-header p {
    color: var(--cream);
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-top: 3rem;
    position: relative;
    z-index: 1;
}

.solution-card {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    padding: 2rem;
    border: 1px solid rgba(201, 166, 100, 0.3);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    backdrop-filter: blur(5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.solution-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(201, 166, 100, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.solution-card:hover {
    transform: translateY(-10px) translateZ(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2), 0 0 20px rgba(201, 166, 100, 0.2);
    border-color: rgba(201, 166, 100, 0.4);
}

.solution-card:hover::before {
    opacity: 1;
}

.solution-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(145deg, rgba(201, 166, 100, 0.2), rgba(201, 166, 100, 0.4));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: var(--cream);
    font-size: 1.5rem;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2), 0 0 15px rgba(201, 166, 100, 0.3);
    position: relative;
    z-index: 1;
    transition: all 0.4s ease;
    border: 1px solid rgba(201, 166, 100, 0.3);
}

.solution-card:hover .solution-icon {
    transform: scale(1.1) translateZ(15px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(201, 166, 100, 0.4);
}

.solution-card h3 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
    color: var(--cream);
    font-family: 'Rajdhani', sans-serif;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.solution-card h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(to right, var(--secondary), transparent);
    transition: width 0.4s ease;
}

.solution-card:hover h3::after {
    width: 100%;
}

.solution-card p {
    color: var(--cream);
    line-height: 1.7;
    font-size: 0.95rem;
    opacity: 0.9;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hero-content {
        max-width: 100%;
    }

    .hero-card {
        width: 260px;
        height: 380px;
    }

    .amm-card-3d-wrapper {
        transform: rotateY(-10deg) rotateX(5deg) translateZ(20px);
    }

    .amm-card-3d-wrapper:hover {
        transform: rotateY(-15deg) rotateX(10deg) translateZ(30px) translateY(-10px);
    }

    .about-content {
        flex-direction: column;
        gap: 3rem;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .solutions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .menu-toggle {
        display: flex;
    }

    .glitch-text {
        font-size: 2.5rem;
    }

    .subtitle {
        font-size: 1.2rem;
    }

    .hero .container {
        gap: 2rem;
    }

    .hero-card {
        width: 260px;
        height: 380px;
    }

    .amm-logo-3d span {
        font-size: 3rem;
    }

    .amm-feature-details h4 {
        font-size: 0.85rem;
    }

    .amm-feature-icon {
        width: 28px;
        height: 28px;
        font-size: 0.85rem;
    }

    .amm-card-3d-wrapper {
        padding: 1.5rem;
    }

    .product-grid {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .solutions-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .feature-card {
        padding: 2rem;
    }

    .solution-card {
        padding: 1.5rem;
    }

    .feature-icon, .solution-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-buttons {
        flex-direction: column;
        width: 100%;
    }

    .btn {
        width: 100%;
        text-align: center;
    }

    .product-tags {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
}

/* Product Gallery Section */
.product-gallery {
    padding: 5rem 0;
    background-color: var(--cream);
    position: relative;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.gallery-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 250px;
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(10, 15, 44, 0.8);
    color: var(--secondary);
    padding: 15px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    text-align: center;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 500;
    font-size: 1.2rem;
}

.gallery-item:hover .gallery-caption {
    transform: translateY(0);
}

/* Product Process Container */
.product-process-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0 4rem;
}

.process-image {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 250px;
}

.process-image.full-width {
    grid-column: 1 / -1;
    height: 400px;
}

.process-image:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.process-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.process-image:hover img {
    transform: scale(1.05);
}

.process-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(10, 15, 44, 0.8);
    color: var(--secondary);
    padding: 15px;
    text-align: center;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 500;
    font-size: 1.2rem;
}

/* New Products Section */
.new-products-header {
    margin-top: 4rem;
    position: relative;
}

.new-products-header::before {
    content: 'NEW';
    position: absolute;
    top: -15px;
    right: 0;
    background: var(--secondary);
    color: var(--primary-dark);
    padding: 5px 15px;
    font-size: 0.8rem;
    font-weight: 700;
    border-radius: 20px;
    letter-spacing: 1px;
    box-shadow: 0 5px 15px rgba(201, 166, 100, 0.3);
}

/* Industry Grid */
.industry-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.industry-card {
    background: rgba(245, 240, 225, 0.05);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(201, 166, 100, 0.2);
}

.industry-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(201, 166, 100, 0.4);
}

.industry-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.industry-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.industry-card:hover .industry-image img {
    transform: scale(1.1);
}

.industry-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(10, 15, 44, 0.9), transparent);
    padding: 20px;
    color: var(--secondary);
    transition: all 0.3s ease;
}

.industry-overlay h3 {
    font-size: 1.5rem;
    margin: 0;
    font-family: 'Rajdhani', sans-serif;
    letter-spacing: 1px;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.industry-content {
    padding: 1.5rem;
}

.industry-content p {
    color: var(--text-dark);
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1.2rem;
}

.industry-products {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.industry-products span {
    padding: 0.3rem 0.8rem;
    background: rgba(201, 166, 100, 0.1);
    border: 1px solid rgba(201, 166, 100, 0.3);
    border-radius: 20px;
    font-size: 0.8rem;
    color: var(--primary);
    transition: all 0.3s ease;
}

.industry-card:hover .industry-products span {
    background: rgba(201, 166, 100, 0.2);
    border-color: rgba(201, 166, 100, 0.5);
}

.industry-process {
    margin-top: 3rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .product-process-container {
        grid-template-columns: 1fr;
    }

    .industry-grid {
        grid-template-columns: 1fr;
    }
}


/* Simple Industries Section */
.simple-industries {
    padding: 5rem 0;
    background-color: #f9f9f9;
    position: relative;
}

.simple-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.simple-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(201, 166, 100, 0.1);
}

.simple-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: rgba(201, 166, 100, 0.3);
}

.simple-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-bottom: 1px solid rgba(201, 166, 100, 0.1);
}

.simple-card h3 {
    color: var(--primary);
    font-size: 1.5rem;
    margin: 1rem 0;
    padding: 0 1.5rem;
    font-weight: 600;
}

.simple-card p {
    color: var(--text-dark);
    font-size: 0.9rem;
    line-height: 1.6;
    padding: 0 1.5rem 1.5rem;
}
