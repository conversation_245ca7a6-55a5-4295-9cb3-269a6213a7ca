# AMM Website Deployment Guide

## 🌐 Deploy to ammindia.in

### Quick Deployment Options

#### Option 1: Netlify (Recommended)
1. Go to https://netlify.com
2. Sign up with GitHub
3. Click "Add new site" → "Import an existing project"
4. Select your AMM repository
5. Configure:
   - Base directory: `AMM-CLEAN-WEBSITE`
   - Build command: `npm run build`
   - Publish directory: `AMM-CLEAN-WEBSITE/dist`
6. Deploy site
7. Add custom domain: ammindia.in

#### Option 2: Vercel
1. Go to https://vercel.com
2. Import AMM repository
3. Set root directory to `AMM-CLEAN-WEBSITE`
4. Deploy automatically
5. Configure custom domain

#### Option 3: GitHub Pages
1. Go to repository Settings
2. Pages section
3. Source: Deploy from branch
4. Branch: main
5. Folder: /AMM-CLEAN-WEBSITE/dist

### Domain Configuration

#### For ammindia.in domain:
1. Go to your domain registrar (GoD<PERSON>dy, Namecheap, etc.)
2. Update DNS settings:
   - A Record: @ → 75.2.60.5 (Netlify)
   - CNAME: www → your-site.netlify.app

#### SSL Certificate:
- Automatically provided by Netlify/Vercel
- Force HTTPS redirect enabled

### Build Information
- Framework: React + Vite
- Build command: `npm run build`
- Output directory: `dist`
- Node version: 18+

### Files Ready for Deployment:
✅ Production build created
✅ _redirects file for SPA routing
✅ netlify.toml configuration
✅ All assets optimized
✅ Git repository updated

### Live Website Features:
🏠 Professional Home Page
📖 About Page with Team Experience
🔧 Advanced Product Grid (8 products)
📞 Contact Form with Product Selection
🎨 Futuristic Design Elements
📱 Mobile Responsive
⚡ Fast Loading (Optimized)

### Post-Deployment:
1. Test all pages and functionality
2. Verify mobile responsiveness
3. Check contact form submission
4. Confirm SSL certificate
5. Set up analytics (optional)

### Support:
- All code is production-ready
- No additional configuration needed
- Automatic deployments on Git push
