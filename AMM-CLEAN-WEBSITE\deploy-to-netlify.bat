@echo off
echo ========================================
echo   AMM Website Deployment to Netlify
echo ========================================
echo.

echo Step 1: Installing Netlify CLI...
npm install -g netlify-cli

echo.
echo Step 2: Building the website...
npm run build

echo.
echo Step 3: Deploying to Netlify...
echo Please follow the prompts to login and deploy
netlify deploy --prod --dir=dist

echo.
echo ========================================
echo   Deployment Complete!
echo ========================================
echo Your website should now be live!
echo.
pause
