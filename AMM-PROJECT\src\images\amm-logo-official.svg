<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="300" height="100" viewBox="0 0 300 100">
  <defs>
    <linearGradient id="amm-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#a0a0a0"/>
      <stop offset="50%" stop-color="#d0d0d0"/>
      <stop offset="100%" stop-color="#f0f0f0"/>
    </linearGradient>
  </defs>
  <g fill="url(#amm-gradient)">
    <!-- A -->
    <path d="M60 20 L30 80 L40 80 L45 70 L75 70 L80 80 L90 80 L60 20 Z M50 40 L60 60 L50 60 Z"/>
    <!-- First M -->
    <path d="M110 20 L110 80 L120 80 L120 40 L140 60 L160 40 L160 80 L170 80 L170 20 L160 20 L140 40 L120 20 Z"/>
    <!-- Second M with arrow -->
    <path d="M190 20 L190 80 L200 80 L200 40 L220 60 L240 40 L240 80 L250 80 L250 20 L240 20 L220 40 L200 20 Z"/>
    <!-- Arrow in the last M -->
    <path d="M240 40 L240 20 L250 30 Z"/>
  </g>
</svg>
