import { Routes, Route, Link } from 'react-router-dom'

function App() {
  return (
    <div className="min-h-screen" style={{ backgroundColor: '#f5f5f5' }}>
      {/* Professional Navigation */}
      <nav className="shadow-lg" style={{ backgroundColor: '#0F3460' }}>
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-white">AMM</h1>
              <span className="ml-2 text-sm text-gray-300">Anuradha Minerals and Metals</span>
            </div>
            <div className="flex space-x-8">
              <Link to="/" className="text-white hover:text-yellow-300 transition-colors">Home</Link>
              <Link to="/about" className="text-white hover:text-yellow-300 transition-colors">About</Link>
              <Link to="/products" className="text-white hover:text-yellow-300 transition-colors">Products</Link>
              <Link to="/contact" className="text-white hover:text-yellow-300 transition-colors">Contact</Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main content */}
      <main className="min-h-screen">
        <Routes>
          <Route path="/" element={
            <div>
              {/* Hero Section */}
              <section className="py-20 px-8" style={{ backgroundColor: '#0F3460', color: 'white' }}>
                <div className="max-w-6xl mx-auto text-center">
                  <h1 className="text-5xl font-bold mb-6">Anuradha Minerals and Metals</h1>
                  <p className="text-xl mb-8 text-gray-300">Leading the Future of Minerals and Metals Trading</p>
                  <div className="inline-block px-8 py-3 rounded-lg" style={{ backgroundColor: '#c9a664' }}>
                    <span className="text-white font-semibold">Excellence in Quality & Service</span>
                  </div>
                </div>
              </section>

              {/* About Section */}
              <section className="py-16 px-8">
                <div className="max-w-6xl mx-auto">
                  <h2 className="text-4xl font-bold mb-12 text-center text-gray-800">About AMM</h2>
                  <div className="grid md:grid-cols-2 gap-12 items-center">
                    <div>
                      <h3 className="text-2xl font-semibold mb-6" style={{ color: '#0F3460' }}>Our Company</h3>
                      <p className="text-gray-700 mb-4 leading-relaxed">
                        Anuradha Minerals and Metals (AMM) is a newly established trading company
                        led by Managing Director Anuradha Singh. We specialize in providing
                        high-quality minerals and metals to industries across the globe.
                      </p>
                      <p className="text-gray-700 leading-relaxed">
                        With a commitment to excellence and customer satisfaction, we have
                        quickly established ourselves as a trusted partner in the minerals
                        and metals trading industry.
                      </p>
                    </div>
                    <div className="bg-white p-8 rounded-lg shadow-lg">
                      <h4 className="text-xl font-semibold mb-4" style={{ color: '#c9a664' }}>Managing Director</h4>
                      <p className="text-gray-700 mb-2"><strong>Anuradha Singh</strong></p>
                      <p className="text-gray-600 text-sm">
                        Leading AMM with vision and dedication to provide exceptional
                        quality products and services to our valued customers.
                      </p>
                    </div>
                  </div>
                </div>
              </section>

              {/* Products Section */}
              <section className="py-16 px-8" style={{ backgroundColor: '#f8f9fa' }}>
                <div className="max-w-6xl mx-auto">
                  <h2 className="text-4xl font-bold mb-12 text-center text-gray-800">Our Products</h2>
                  <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {[
                      { name: 'Aluminium Ingots', desc: 'High-grade aluminium ingots for industrial applications' },
                      { name: 'Calcined Lime', desc: 'Premium quality calcined lime for various industries' },
                      { name: 'Glycerin', desc: 'Pure glycerin for pharmaceutical and cosmetic use' },
                      { name: 'Scrap Materials', desc: 'Ferrous and non-ferrous scrap materials' }
                    ].map((product, index) => (
                      <div key={index} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                        <h3 className="text-lg font-semibold mb-3" style={{ color: '#0F3460' }}>{product.name}</h3>
                        <p className="text-gray-600 text-sm">{product.desc}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </section>
            </div>
          } />
          <Route path="/about" element={
            <div className="py-16 px-8">
              <div className="max-w-4xl mx-auto">
                <h1 className="text-4xl font-bold mb-8 text-center text-gray-800">About AMM</h1>
                <div className="bg-white p-8 rounded-lg shadow-lg">
                  <h2 className="text-2xl font-semibold mb-6" style={{ color: '#0F3460' }}>Our Story</h2>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    Anuradha Minerals and Metals (AMM) was established with a vision to become
                    a leading player in the minerals and metals trading industry. Under the
                    leadership of Managing Director Anuradha Singh, we have built a reputation
                    for excellence and reliability.
                  </p>
                  <h3 className="text-xl font-semibold mb-4" style={{ color: '#c9a664' }}>Our Mission</h3>
                  <p className="text-gray-700 mb-6">
                    To provide high-quality minerals and metals while maintaining the highest
                    standards of customer service and business ethics.
                  </p>
                  <h3 className="text-xl font-semibold mb-4" style={{ color: '#c9a664' }}>Our Vision</h3>
                  <p className="text-gray-700">
                    To be recognized as a trusted global partner in the minerals and metals
                    trading industry, known for our commitment to quality and innovation.
                  </p>
                </div>
              </div>
            </div>
          } />
          <Route path="/products" element={
            <div className="py-16 px-8">
              <div className="max-w-6xl mx-auto">
                <h1 className="text-4xl font-bold mb-12 text-center text-gray-800">Our Products</h1>
                <div className="grid md:grid-cols-2 gap-8">
                  {[
                    {
                      name: 'Aluminium Ingots',
                      desc: 'High-purity aluminium ingots suitable for various industrial applications including automotive, aerospace, and construction industries.',
                      features: ['99.7% purity', 'Various sizes available', 'Certified quality']
                    },
                    {
                      name: 'Calcined Lime',
                      desc: 'Premium quality calcined lime for steel production, water treatment, and chemical processing applications.',
                      features: ['High calcium content', 'Low impurities', 'Consistent quality']
                    },
                    {
                      name: 'Glycerin',
                      desc: 'Pure glycerin for pharmaceutical, cosmetic, and food industry applications.',
                      features: ['USP grade available', 'Food grade quality', 'Pharmaceutical grade']
                    },
                    {
                      name: 'Scrap Materials',
                      desc: 'High-quality ferrous and non-ferrous scrap materials for recycling and reprocessing.',
                      features: ['Sorted and graded', 'Competitive pricing', 'Reliable supply']
                    }
                  ].map((product, index) => (
                    <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                      <h2 className="text-2xl font-semibold mb-4" style={{ color: '#0F3460' }}>{product.name}</h2>
                      <p className="text-gray-700 mb-6">{product.desc}</p>
                      <h3 className="text-lg font-semibold mb-3" style={{ color: '#c9a664' }}>Key Features:</h3>
                      <ul className="text-gray-600 space-y-1">
                        {product.features.map((feature, idx) => (
                          <li key={idx}>• {feature}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          } />
          <Route path="/contact" element={
            <div className="py-16 px-8">
              <div className="max-w-4xl mx-auto">
                <h1 className="text-4xl font-bold mb-12 text-center text-gray-800">Contact Us</h1>
                <div className="grid md:grid-cols-2 gap-8">
                  <div className="bg-white p-8 rounded-lg shadow-lg">
                    <h2 className="text-2xl font-semibold mb-6" style={{ color: '#0F3460' }}>Get in Touch</h2>
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-semibold text-gray-800">Managing Director</h3>
                        <p className="text-gray-600">Anuradha Singh</p>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800">Company</h3>
                        <p className="text-gray-600">Anuradha Minerals and Metals</p>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800">Website</h3>
                        <p className="text-gray-600">www.ammindia.in</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-white p-8 rounded-lg shadow-lg">
                    <h2 className="text-2xl font-semibold mb-6" style={{ color: '#0F3460' }}>Send Message</h2>
                    <form className="space-y-4">
                      <input
                        type="text"
                        placeholder="Your Name"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                      />
                      <input
                        type="email"
                        placeholder="Your Email"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                      />
                      <textarea
                        placeholder="Your Message"
                        rows="4"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                      ></textarea>
                      <button
                        type="submit"
                        className="w-full py-3 rounded-lg text-white font-semibold hover:opacity-90 transition-opacity"
                        style={{ backgroundColor: '#0F3460' }}
                      >
                        Send Message
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          } />
          <Route path="*" element={
            <div className="py-16 px-8 text-center">
              <h1 className="text-4xl font-bold mb-4 text-gray-800">Welcome to AMM</h1>
              <p className="text-gray-600">Navigate using the menu above to explore our company and products.</p>
            </div>
          } />
        </Routes>
      </main>

      {/* Professional footer */}
      <footer className="text-white py-12 px-8" style={{ backgroundColor: '#0F3460' }}>
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-2xl font-bold mb-4">AMM</h3>
              <p className="text-gray-300 mb-4">
                Anuradha Minerals and Metals - Leading the future of minerals and metals trading.
              </p>
              <p className="text-gray-300 text-sm">
                Excellence in Quality & Service
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Our Products</h4>
              <ul className="text-gray-300 space-y-2 text-sm">
                <li>Aluminium Ingots</li>
                <li>Calcined Lime</li>
                <li>Glycerin</li>
                <li>Scrap Materials</li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
              <div className="text-gray-300 space-y-2 text-sm">
                <p>Managing Director: Anuradha Singh</p>
                <p>Website: www.ammindia.in</p>
                <p>Company: Anuradha Minerals and Metals</p>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-600 mt-8 pt-8 text-center">
            <p className="text-gray-300 text-sm">
              © 2024 Anuradha Minerals and Metals. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default App
