import { Routes, Route } from 'react-router-dom'

function App() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>AMM Website - Step by Step Loading</h1>

      <Routes>
        <Route path="/" element={
          <div>
            <h2>Home Page</h2>
            <p>Basic routing is working!</p>
            <p>Anuradha Minerals and Metals</p>
          </div>
        } />
        <Route path="/about" element={
          <div>
            <h2>About Page</h2>
            <p>About page is working!</p>
          </div>
        } />
        <Route path="*" element={
          <div>
            <h2>Home Page (Default)</h2>
            <p>Basic routing is working!</p>
            <p>Anuradha Minerals and Metals</p>
          </div>
        } />
      </Routes>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0' }}>
        <p>Navigation test:</p>
        <a href="/" style={{ marginRight: '10px' }}>Home</a>
        <a href="/about">About</a>
      </div>
    </div>
  )
}

export default App
