import { Routes, Route } from 'react-router-dom'

function App() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Simple header */}
      <header className="bg-blue-900 text-white p-4">
        <h1 className="text-2xl font-bold">AMM - Anuradha Minerals and Metals</h1>
      </header>

      {/* Main content */}
      <main className="p-8">
        <Routes>
          <Route path="/" element={
            <div>
              <h2 className="text-3xl font-bold mb-6 text-gray-800">Welcome to AMM</h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="bg-white p-6 rounded-lg shadow-md">
                  <h3 className="text-xl font-semibold mb-4 text-blue-600">About Us</h3>
                  <p className="text-gray-700">
                    Anuradha Minerals and Metals (AMM) is a leading trading company
                    specializing in high-quality minerals and metals.
                  </p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow-md">
                  <h3 className="text-xl font-semibold mb-4 text-green-600">Our Products</h3>
                  <ul className="text-gray-700 space-y-2">
                    <li>• Aluminium Ingots</li>
                    <li>• Calcined Lime</li>
                    <li>• Glycerin</li>
                    <li>• Scrap (Ferrous and Non-Ferrous)</li>
                  </ul>
                </div>
              </div>
            </div>
          } />
          <Route path="/about" element={
            <div>
              <h2 className="text-3xl font-bold mb-6 text-gray-800">About AMM</h2>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <p className="text-gray-700 mb-4">
                  Led by Managing Director Anuradha Singh, AMM is committed to
                  providing exceptional quality products and services.
                </p>
              </div>
            </div>
          } />
          <Route path="*" element={
            <div>
              <h2 className="text-3xl font-bold mb-6 text-gray-800">Welcome to AMM</h2>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <p className="text-gray-700">
                  Navigate to different sections using the menu above.
                </p>
              </div>
            </div>
          } />
        </Routes>
      </main>

      {/* Simple footer */}
      <footer className="bg-gray-800 text-white p-4 mt-8">
        <p className="text-center">© 2024 Anuradha Minerals and Metals. All rights reserved.</p>
      </footer>
    </div>
  )
}

export default App
