import { Routes, Route } from 'react-router-dom'

function App() {
  return (
    <div className="p-8 bg-gray-100 min-h-screen">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">AMM Website - Testing TailwindCSS</h1>

      <Routes>
        <Route path="/" element={
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-semibold text-blue-600 mb-4">Home Page</h2>
            <p className="text-gray-700 mb-2">TailwindCSS is working!</p>
            <p className="text-lg font-medium text-gray-900">Anuradha Minerals and Metals</p>
          </div>
        } />
        <Route path="/about" element={
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-semibold text-green-600 mb-4">About Page</h2>
            <p className="text-gray-700">About page with TailwindCSS styling!</p>
          </div>
        } />
        <Route path="*" element={
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-semibold text-blue-600 mb-4">Home Page (Default)</h2>
            <p className="text-gray-700 mb-2">TailwindCSS is working!</p>
            <p className="text-lg font-medium text-gray-900">Anuradha Minerals and Metals</p>
          </div>
        } />
      </Routes>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <p className="text-gray-700 mb-2">Navigation test:</p>
        <a href="/" className="text-blue-600 hover:text-blue-800 mr-4 underline">Home</a>
        <a href="/about" className="text-blue-600 hover:text-blue-800 underline">About</a>
      </div>
    </div>
  )
}

export default App
